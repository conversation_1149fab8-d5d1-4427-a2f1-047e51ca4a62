<?php
$title = "Contact - " . getSetting('site_title', SITE_NAME);
$description = "Get in touch with " . getSetting('site_title', SITE_NAME) . " for your photography and video production needs.";

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $phone = sanitize($_POST['phone'] ?? '');
    $subject = sanitize($_POST['subject'] ?? '');
    $messageText = sanitize($_POST['message'] ?? '');
    
    // Validation
    $errors = [];
    
    if (empty($name)) $errors[] = 'Name is required';
    if (empty($email)) $errors[] = 'Email is required';
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'Valid email is required';
    if (empty($messageText)) $errors[] = 'Message is required';
    
    if (empty($errors)) {
        try {
            // Save to database
            $db = Database::getInstance();
            $sql = "INSERT INTO contact_messages (name, email, phone, subject, message) VALUES (?, ?, ?, ?, ?)";
            $db->query($sql, [$name, $email, $phone, $subject, $messageText]);
            
            // Send email notification (if configured)
            $adminEmail = getSetting('contact_email', ADMIN_EMAIL);
            if (!empty($adminEmail)) {
                $emailSubject = "New Contact Form Submission - " . getSetting('site_title', SITE_NAME);
                $emailBody = "New contact form submission:\n\n";
                $emailBody .= "Name: $name\n";
                $emailBody .= "Email: $email\n";
                $emailBody .= "Phone: $phone\n";
                $emailBody .= "Subject: $subject\n\n";
                $emailBody .= "Message:\n$messageText\n";
                
                $headers = "From: $email\r\n";
                $headers .= "Reply-To: $email\r\n";
                
                @mail($adminEmail, $emailSubject, $emailBody, $headers);
            }
            
            $message = 'Thank you for your message! We\'ll get back to you soon.';
            $messageType = 'success';
            
            // Clear form data
            $_POST = [];
            
        } catch (Exception $e) {
            $message = 'Sorry, there was an error sending your message. Please try again.';
            $messageType = 'error';
        }
    } else {
        $message = implode('<br>', $errors);
        $messageType = 'error';
    }
    
    // Return JSON for AJAX requests
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => $messageType === 'success',
            'message' => $message
        ]);
        exit;
    }
}

includeHeader($title, $description);
?>

<!-- Page Header -->
<section class="section bg-gradient text-white overlay-dark">
    <div class="container text-center">
        <h1 class="display-4 fw-bold animate-on-scroll">Contact Us</h1>
        <p class="lead animate-on-scroll">Let's discuss your photography and video production needs</p>
    </div>
</section>

<!-- Contact Section -->
<section class="section">
    <div class="container">
        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-8 mb-5">
                <div class="card animate-on-scroll">
                    <div class="card-header">
                        <h3 class="mb-0">Send us a Message</h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($message)) { ?>
                        <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php } ?>
                        
                        <form id="contactForm" method="POST" action="/contact">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="subject" class="form-label">Subject</label>
                                    <select class="form-select" id="subject" name="subject">
                                        <option value="">Select a subject</option>
                                        <option value="Wedding Photography" <?php echo ($_POST['subject'] ?? '') === 'Wedding Photography' ? 'selected' : ''; ?>>Wedding Photography</option>
                                        <option value="Portrait Session" <?php echo ($_POST['subject'] ?? '') === 'Portrait Session' ? 'selected' : ''; ?>>Portrait Session</option>
                                        <option value="Commercial Photography" <?php echo ($_POST['subject'] ?? '') === 'Commercial Photography' ? 'selected' : ''; ?>>Commercial Photography</option>
                                        <option value="Video Production" <?php echo ($_POST['subject'] ?? '') === 'Video Production' ? 'selected' : ''; ?>>Video Production</option>
                                        <option value="Event Coverage" <?php echo ($_POST['subject'] ?? '') === 'Event Coverage' ? 'selected' : ''; ?>>Event Coverage</option>
                                        <option value="General Inquiry" <?php echo ($_POST['subject'] ?? '') === 'General Inquiry' ? 'selected' : ''; ?>>General Inquiry</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">Message *</label>
                                <textarea class="form-control" id="message" name="message" rows="6" 
                                          placeholder="Tell us about your project, event date, location, and any specific requirements..." required><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>Send Message
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="col-lg-4">
                <div class="card animate-on-scroll">
                    <div class="card-header">
                        <h4 class="mb-0">Get in Touch</h4>
                    </div>
                    <div class="card-body">
                        <?php
                        $email = getSetting('contact_email');
                        $phone = getSetting('contact_phone');
                        $address = getSetting('contact_address');
                        
                        if (!empty($email)) {
                        ?>
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-envelope fa-lg text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Email</h6>
                                    <a href="mailto:<?php echo htmlspecialchars($email); ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($email); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php } ?>
                        
                        <?php if (!empty($phone)) { ?>
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-phone fa-lg text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Phone</h6>
                                    <a href="tel:<?php echo htmlspecialchars(preg_replace('/[^0-9+]/', '', $phone)); ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($phone); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php } ?>
                        
                        <?php if (!empty($address)) { ?>
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-start">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-map-marker-alt fa-lg text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Address</h6>
                                    <p class="mb-0"><?php echo nl2br(htmlspecialchars($address)); ?></p>
                                </div>
                            </div>
                        </div>
                        <?php } ?>
                        
                        <!-- Social Media -->
                        <div class="contact-item">
                            <h6 class="mb-3">Follow Us</h6>
                            <div class="social-links">
                                <?php
                                $social_links = [
                                    'instagram' => ['icon' => 'fab fa-instagram', 'label' => 'Instagram'],
                                    'facebook' => ['icon' => 'fab fa-facebook', 'label' => 'Facebook'],
                                    'twitter' => ['icon' => 'fab fa-twitter', 'label' => 'Twitter'],
                                    'youtube' => ['icon' => 'fab fa-youtube', 'label' => 'YouTube']
                                ];
                                
                                foreach ($social_links as $platform => $info) {
                                    $url = getSetting('social_' . $platform);
                                    if (!empty($url)) {
                                        echo '<a href="' . htmlspecialchars($url) . '" class="btn btn-outline-primary btn-sm me-2 mb-2" target="_blank" rel="noopener" aria-label="' . $info['label'] . '">
                                                <i class="' . $info['icon'] . '"></i>
                                              </a>';
                                    }
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Business Hours -->
                <div class="card mt-4 animate-on-scroll">
                    <div class="card-header">
                        <h5 class="mb-0">Business Hours</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Monday - Friday</span>
                            <span>9:00 AM - 6:00 PM</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Saturday</span>
                            <span>10:00 AM - 4:00 PM</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Sunday</span>
                            <span>By Appointment</span>
                        </div>
                        <hr>
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Evening and weekend shoots available by appointment
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="section bg-light">
    <div class="container">
        <h2 class="section-title animate-on-scroll">Quick Questions</h2>
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card h-100 animate-on-scroll">
                    <div class="card-body">
                        <h5><i class="fas fa-clock text-primary me-2"></i>Response Time</h5>
                        <p class="mb-0">We typically respond to inquiries within 24 hours during business days.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card h-100 animate-on-scroll">
                    <div class="card-body">
                        <h5><i class="fas fa-calendar text-primary me-2"></i>Booking</h5>
                        <p class="mb-0">We recommend booking 3-6 months in advance for weddings and major events.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card h-100 animate-on-scroll">
                    <div class="card-body">
                        <h5><i class="fas fa-map-marked-alt text-primary me-2"></i>Travel</h5>
                        <p class="mb-0">We're happy to travel for your event. Travel fees may apply for distant locations.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card h-100 animate-on-scroll">
                    <div class="card-body">
                        <h5><i class="fas fa-dollar-sign text-primary me-2"></i>Pricing</h5>
                        <p class="mb-0">Custom quotes provided based on your specific needs and requirements.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php includeFooter(); ?>
