<?php
$title = getSetting('site_title', SITE_NAME);
$description = getSetting('site_tagline', SITE_DESCRIPTION);
includeHeader($title, $description);

// Get models
$galleryModel = new Gallery();
$mediaModel = new MediaItem();

// Get featured galleries and recent work
$featuredGalleries = $galleryModel->getAllActive(true);
$recentMedia = $mediaModel->getRecent(8);
?>

<!-- Hero Section -->
<section class="hero-section">
    <?php 
    $heroVideo = getSetting('hero_video');
    $heroImage = getSetting('hero_image');
    
    if (!empty($heroVideo) && file_exists($heroVideo)) {
        echo '<video class="hero-video" autoplay muted loop playsinline>
                <source src="' . htmlspecialchars($heroVideo) . '" type="video/mp4">
              </video>';
    } elseif (!empty($heroImage) && file_exists($heroImage)) {
        echo '<div class="hero-video" style="background-image: url(\'' . htmlspecialchars($heroImage) . '\'); background-size: cover; background-position: center;"></div>';
    }
    ?>
    
    <div class="hero-overlay"></div>
    
    <div class="hero-content">
        <h1 class="hero-title animate-on-scroll">
            <?php echo getSetting('hero_title', 'Capturing Life\'s Moments'); ?>
        </h1>
        <p class="hero-subtitle animate-on-scroll">
            <?php echo getSetting('hero_subtitle', 'Professional Photography & Video Production'); ?>
        </p>
        <div class="hero-cta animate-on-scroll">
            <a href="/portfolio" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-images me-2"></i>View Portfolio
            </a>
            <a href="/contact" class="btn btn-outline-light btn-lg">
                <i class="fas fa-envelope me-2"></i>Get In Touch
            </a>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="section bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <h2 class="section-title text-start animate-on-scroll">
                    About <?php echo getSetting('site_title', SITE_NAME); ?>
                </h2>
                <div class="animate-on-scroll">
                    <?php echo nl2br(htmlspecialchars(getSetting('about_text', 'Welcome to our photography studio where creativity meets technical excellence.'))); ?>
                </div>
                <div class="mt-4 animate-on-scroll">
                    <a href="/about" class="btn btn-primary">
                        <i class="fas fa-user me-2"></i>Learn More
                    </a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="row">
                    <?php
                    // Display first 4 recent media items as about preview
                    $aboutMedia = array_slice($recentMedia, 0, 4);
                    foreach ($aboutMedia as $item) {
                        $thumbnail = !empty($item['thumbnail_path']) ? $item['thumbnail_path'] : $item['file_path'];
                        echo '<div class="col-6 mb-3">
                                <div class="card animate-on-scroll">
                                    <img src="' . htmlspecialchars($thumbnail) . '" class="card-img-top" alt="' . htmlspecialchars($item['title']) . '" style="height: 150px; object-fit: cover;">
                                </div>
                              </div>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Galleries Section -->
<?php if (!empty($featuredGalleries)) { ?>
<section class="section">
    <div class="container">
        <h2 class="section-title animate-on-scroll">Featured Galleries</h2>
        <div class="row">
            <?php foreach ($featuredGalleries as $gallery) { 
                $coverImage = !empty($gallery['cover_image']) ? $gallery['cover_image'] : 'assets/images/placeholder-gallery.jpg';
            ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 animate-on-scroll">
                    <img src="<?php echo htmlspecialchars($coverImage); ?>" class="card-img-top" alt="<?php echo htmlspecialchars($gallery['title']); ?>" style="height: 250px; object-fit: cover;">
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title"><?php echo htmlspecialchars($gallery['title']); ?></h5>
                        <p class="card-text flex-grow-1"><?php echo htmlspecialchars(substr($gallery['description'], 0, 100)) . (strlen($gallery['description']) > 100 ? '...' : ''); ?></p>
                        <a href="/gallery/<?php echo htmlspecialchars($gallery['slug']); ?>" class="btn btn-primary mt-auto">
                            <i class="fas fa-eye me-2"></i>View Gallery
                        </a>
                    </div>
                </div>
            </div>
            <?php } ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="/portfolio" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-images me-2"></i>View All Galleries
            </a>
        </div>
    </div>
</section>
<?php } ?>

<!-- Recent Work Section -->
<?php if (!empty($recentMedia)) { ?>
<section class="section bg-light">
    <div class="container">
        <h2 class="section-title animate-on-scroll">Recent Work</h2>
        <div class="masonry-grid">
            <?php foreach ($recentMedia as $item) { 
                $thumbnail = !empty($item['thumbnail_path']) ? $item['thumbnail_path'] : $item['file_path'];
                $isVideo = $item['file_type'] === 'video';
            ?>
            <div class="masonry-item" data-fancybox="recent-work" 
                 data-src="<?php echo htmlspecialchars($item['file_path']); ?>"
                 data-caption="<?php echo htmlspecialchars($item['title'] . ' - ' . $item['gallery_title']); ?>">
                <?php if ($isVideo) { ?>
                    <video style="width: 100%; height: auto; display: block;" muted>
                        <source src="<?php echo htmlspecialchars($thumbnail); ?>" type="video/mp4">
                    </video>
                    <div class="position-absolute top-50 start-50 translate-middle">
                        <i class="fas fa-play-circle fa-3x text-white opacity-75"></i>
                    </div>
                <?php } else { ?>
                    <img src="<?php echo htmlspecialchars($thumbnail); ?>" alt="<?php echo htmlspecialchars($item['title']); ?>" loading="lazy">
                <?php } ?>
            </div>
            <?php } ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="/portfolio" class="btn btn-primary btn-lg">
                <i class="fas fa-camera me-2"></i>See More Work
            </a>
        </div>
    </div>
</section>
<?php } ?>

<!-- Services Preview Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title animate-on-scroll">Our Services</h2>
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card text-center h-100 animate-on-scroll">
                    <div class="card-body">
                        <i class="fas fa-camera fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Photography</h5>
                        <p class="card-text">Professional photography for portraits, weddings, food, products, and real estate.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card text-center h-100 animate-on-scroll">
                    <div class="card-body">
                        <i class="fas fa-video fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Video Production</h5>
                        <p class="card-text">Creative video content for weddings, corporate events, commercials, and documentaries.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card text-center h-100 animate-on-scroll">
                    <div class="card-body">
                        <i class="fas fa-globe fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">360° Virtual Tours</h5>
                        <p class="card-text">Immersive 360-degree virtual tours for businesses, real estate, and interactive experiences.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card text-center h-100 animate-on-scroll">
                    <div class="card-body">
                        <i class="fas fa-utensils fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Food Photography</h5>
                        <p class="card-text">Mouth-watering food photography for restaurants, menus, and culinary brands.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card text-center h-100 animate-on-scroll">
                    <div class="card-body">
                        <i class="fas fa-cube fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Product Photography</h5>
                        <p class="card-text">High-quality product shots for e-commerce, marketing, and brand promotion.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card text-center h-100 animate-on-scroll">
                    <div class="card-body">
                        <i class="fas fa-home fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Real Estate</h5>
                        <p class="card-text">Professional real estate photography showcasing properties at their absolute best.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="/services" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-list me-2"></i>View All Services
            </a>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="section bg-gradient text-white overlay-dark">
    <div class="container text-center">
        <h2 class="animate-on-scroll">Ready to Capture Your Story?</h2>
        <p class="lead mb-4 animate-on-scroll">Let's create something beautiful together. Get in touch to discuss your project.</p>
        <div class="animate-on-scroll">
            <a href="/contact" class="btn btn-light btn-lg me-3">
                <i class="fas fa-phone me-2"></i>Contact Us
            </a>
            <a href="/portfolio" class="btn btn-outline-light btn-lg">
                <i class="fas fa-eye me-2"></i>View Portfolio
            </a>
        </div>
    </div>
</section>

<?php includeFooter(); ?>
