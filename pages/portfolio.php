<?php
$title = "Portfolio - " . getSetting('site_title', SITE_NAME);
$description = "Explore our photography and video portfolio showcasing our best work across various categories.";
includeHeader($title, $description);

$galleryModel = new Gallery();
$galleries = $galleryModel->getAllActive();
?>

<!-- Page Header -->
<section class="section bg-gradient text-white overlay-dark">
    <div class="container text-center">
        <h1 class="display-4 fw-bold animate-on-scroll">Portfolio</h1>
        <p class="lead animate-on-scroll">Explore our collection of photography and video work</p>
    </div>
</section>

<!-- Portfolio Galleries -->
<section class="section">
    <div class="container">
        <?php if (!empty($galleries)) { ?>
        <div class="row">
            <?php foreach ($galleries as $gallery) { 
                $coverImage = !empty($gallery['cover_image']) ? $gallery['cover_image'] : 'assets/images/placeholder-gallery.jpg';
                
                // Get media count for this gallery
                $mediaModel = new MediaItem();
                $mediaItems = $mediaModel->getByGalleryId($gallery['id']);
                $mediaCount = count($mediaItems);
            ?>
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="card h-100 animate-on-scroll">
                    <div class="position-relative">
                        <img src="<?php echo htmlspecialchars($coverImage); ?>" 
                             class="card-img-top" 
                             alt="<?php echo htmlspecialchars($gallery['title']); ?>" 
                             style="height: 300px; object-fit: cover;">
                        
                        <!-- Media count badge -->
                        <div class="position-absolute top-0 end-0 m-3">
                            <span class="badge bg-dark bg-opacity-75">
                                <i class="fas fa-images me-1"></i><?php echo $mediaCount; ?>
                            </span>
                        </div>
                        
                        <!-- Gallery overlay -->
                        <div class="gallery-overlay">
                            <div class="gallery-overlay-content">
                                <h5 class="text-white mb-2"><?php echo htmlspecialchars($gallery['title']); ?></h5>
                                <p class="text-white-50 mb-3"><?php echo $mediaCount; ?> items</p>
                                <a href="/gallery/<?php echo htmlspecialchars($gallery['slug']); ?>" 
                                   class="btn btn-light">
                                    <i class="fas fa-eye me-2"></i>View Gallery
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <h5 class="card-title"><?php echo htmlspecialchars($gallery['title']); ?></h5>
                        <p class="card-text text-muted">
                            <?php 
                            $description = $gallery['description'];
                            echo htmlspecialchars(strlen($description) > 120 ? substr($description, 0, 120) . '...' : $description); 
                            ?>
                        </p>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-images me-1"></i><?php echo $mediaCount; ?> items
                            </small>
                            <?php if ($gallery['is_featured']) { ?>
                            <span class="badge bg-primary">Featured</span>
                            <?php } ?>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-transparent">
                        <a href="/gallery/<?php echo htmlspecialchars($gallery['slug']); ?>" 
                           class="btn btn-primary w-100">
                            <i class="fas fa-eye me-2"></i>View Gallery
                        </a>
                    </div>
                </div>
            </div>
            <?php } ?>
        </div>
        
        <?php } else { ?>
        <!-- No galleries message -->
        <div class="text-center py-5">
            <i class="fas fa-images fa-4x text-muted mb-4"></i>
            <h3 class="text-muted">No Galleries Available</h3>
            <p class="text-muted">Galleries will appear here once they are added by the administrator.</p>
            <a href="/contact" class="btn btn-primary">
                <i class="fas fa-envelope me-2"></i>Contact Us
            </a>
        </div>
        <?php } ?>
    </div>
</section>

<!-- Featured Work Section -->
<?php 
$mediaModel = new MediaItem();
$featuredMedia = $mediaModel->getFeatured(12);

if (!empty($featuredMedia)) { ?>
<section class="section bg-light">
    <div class="container">
        <h2 class="section-title animate-on-scroll">Featured Work</h2>
        <div class="masonry-grid">
            <?php foreach ($featuredMedia as $item) { 
                $thumbnail = !empty($item['thumbnail_path']) ? $item['thumbnail_path'] : $item['file_path'];
                $isVideo = $item['file_type'] === 'video';
            ?>
            <div class="masonry-item" 
                 data-fancybox="featured-work" 
                 data-src="<?php echo htmlspecialchars($item['file_path']); ?>"
                 data-caption="<?php echo htmlspecialchars($item['title'] . ' - ' . $item['gallery_title']); ?>">
                
                <?php if ($isVideo) { ?>
                    <div class="position-relative">
                        <video style="width: 100%; height: auto; display: block;" muted>
                            <source src="<?php echo htmlspecialchars($thumbnail); ?>" type="video/mp4">
                        </video>
                        <div class="position-absolute top-50 start-50 translate-middle">
                            <i class="fas fa-play-circle fa-3x text-white opacity-75"></i>
                        </div>
                    </div>
                <?php } else { ?>
                    <img src="<?php echo htmlspecialchars($thumbnail); ?>" 
                         alt="<?php echo htmlspecialchars($item['title']); ?>" 
                         loading="lazy">
                <?php } ?>
                
                <!-- Item info overlay -->
                <div class="gallery-overlay">
                    <div class="gallery-overlay-content">
                        <h6 class="text-white mb-1"><?php echo htmlspecialchars($item['title'] ?: 'Untitled'); ?></h6>
                        <small class="text-white-50"><?php echo htmlspecialchars($item['gallery_title']); ?></small>
                    </div>
                </div>
            </div>
            <?php } ?>
        </div>
    </div>
</section>
<?php } ?>

<!-- Services Preview -->
<section class="section">
    <div class="container">
        <h2 class="section-title animate-on-scroll">Our Services</h2>
        <div class="row">
            <div class="col-lg-4 mb-4">
                <div class="card text-center h-100 animate-on-scroll">
                    <div class="card-body">
                        <i class="fas fa-camera fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Photography Sessions</h5>
                        <p class="card-text">Professional photography for portraits, events, commercial projects, and artistic endeavors.</p>
                        <a href="/services" class="btn btn-outline-primary">Learn More</a>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card text-center h-100 animate-on-scroll">
                    <div class="card-body">
                        <i class="fas fa-video fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Video Production</h5>
                        <p class="card-text">Creative video content for weddings, corporate events, commercials, and documentaries.</p>
                        <a href="/services" class="btn btn-outline-primary">Learn More</a>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card text-center h-100 animate-on-scroll">
                    <div class="card-body">
                        <i class="fas fa-edit fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Post-Production</h5>
                        <p class="card-text">Professional editing, color grading, and enhancement services for photos and videos.</p>
                        <a href="/services" class="btn btn-outline-primary">Learn More</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="section bg-gradient text-white overlay-dark">
    <div class="container text-center">
        <h2 class="animate-on-scroll">Ready to Create Something Amazing?</h2>
        <p class="lead mb-4 animate-on-scroll">Let's discuss your project and bring your vision to life.</p>
        <div class="animate-on-scroll">
            <a href="/contact" class="btn btn-light btn-lg me-3">
                <i class="fas fa-phone me-2"></i>Get Started
            </a>
            <a href="/about" class="btn btn-outline-light btn-lg">
                <i class="fas fa-user me-2"></i>Learn About Us
            </a>
        </div>
    </div>
</section>

<?php includeFooter(); ?>
