<?php
// Get gallery slug from route parameters
$gallerySlug = $GLOBALS['route_params']['param'] ?? '';

if (empty($gallerySlug)) {
    header('Location: /portfolio');
    exit;
}

$galleryModel = new Gallery();
$mediaModel = new MediaItem();

// Get gallery by slug
$gallery = $galleryModel->getBySlug($gallerySlug);

if (!$gallery) {
    http_response_code(404);
    include 'pages/404.php';
    exit;
}

// Get media items for this gallery
$mediaItems = $mediaModel->getByGalleryId($gallery['id']);

$title = htmlspecialchars($gallery['title']) . " - " . getSetting('site_title', SITE_NAME);
$description = htmlspecialchars($gallery['description']) ?: "View " . htmlspecialchars($gallery['title']) . " gallery";
includeHeader($title, $description);

// Add structured data for gallery
$galleryImages = array_filter($mediaItems, function($item) {
    return $item['file_type'] === 'image';
});

$imageUrls = array_map(function($item) {
    return SITE_URL . '/' . $item['file_path'];
}, array_slice($galleryImages, 0, 10)); // Limit to 10 images

echo generateStructuredData('imageGallery', [
    'title' => $gallery['title'],
    'description' => $gallery['description'],
    'slug' => $gallery['slug'],
    'images' => $imageUrls
]);
?>

<!-- Gallery Header -->
<section class="section bg-gradient text-white overlay-dark">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb" class="mb-3">
                    <ol class="breadcrumb text-white-50">
                        <li class="breadcrumb-item"><a href="/" class="text-white-50">Home</a></li>
                        <li class="breadcrumb-item"><a href="/portfolio" class="text-white-50">Portfolio</a></li>
                        <li class="breadcrumb-item active text-white"><?php echo htmlspecialchars($gallery['title']); ?></li>
                    </ol>
                </nav>
                
                <h1 class="display-4 fw-bold animate-on-scroll"><?php echo htmlspecialchars($gallery['title']); ?></h1>
                
                <?php if (!empty($gallery['description'])) { ?>
                <p class="lead animate-on-scroll"><?php echo htmlspecialchars($gallery['description']); ?></p>
                <?php } ?>
                
                <div class="animate-on-scroll">
                    <span class="badge bg-light text-dark me-2">
                        <i class="fas fa-images me-1"></i><?php echo count($mediaItems); ?> items
                    </span>
                    <?php if ($gallery['is_featured']) { ?>
                    <span class="badge bg-primary">Featured Gallery</span>
                    <?php } ?>
                </div>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="animate-on-scroll">
                    <a href="/portfolio" class="btn btn-outline-light me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Portfolio
                    </a>
                    <a href="/contact" class="btn btn-light">
                        <i class="fas fa-envelope me-2"></i>Contact
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Gallery Content -->
<section class="section">
    <div class="container">
        <?php if (!empty($mediaItems)) { ?>
        
        <!-- Gallery Filter (if needed for categories) -->
        <div class="text-center mb-4">
            <div class="btn-group" role="group" aria-label="Gallery filters">
                <button type="button" class="btn btn-outline-primary active filter-btn" data-filter="all">
                    All (<?php echo count($mediaItems); ?>)
                </button>
                <?php
                // Count images and videos
                $imageCount = 0;
                $videoCount = 0;
                foreach ($mediaItems as $item) {
                    if ($item['file_type'] === 'image') $imageCount++;
                    else $videoCount++;
                }
                
                if ($imageCount > 0) {
                    echo '<button type="button" class="btn btn-outline-primary filter-btn" data-filter="image">
                            <i class="fas fa-image me-1"></i>Photos (' . $imageCount . ')
                          </button>';
                }
                
                if ($videoCount > 0) {
                    echo '<button type="button" class="btn btn-outline-primary filter-btn" data-filter="video">
                            <i class="fas fa-video me-1"></i>Videos (' . $videoCount . ')
                          </button>';
                }
                ?>
            </div>
        </div>
        
        <!-- Gallery Grid -->
        <div class="masonry-grid" id="galleryGrid">
            <?php foreach ($mediaItems as $index => $item) { 
                $thumbnail = !empty($item['thumbnail_path']) ? $item['thumbnail_path'] : $item['file_path'];
                $isVideo = $item['file_type'] === 'video';
                $caption = htmlspecialchars($item['title'] ?: 'Untitled');
                if (!empty($item['description'])) {
                    $caption .= ' - ' . htmlspecialchars($item['description']);
                }
            ?>
            <div class="masonry-item gallery-item animate-on-scroll" 
                 data-category="<?php echo $item['file_type']; ?>"
                 data-fancybox="gallery" 
                 data-src="<?php echo htmlspecialchars($item['file_path']); ?>"
                 data-caption="<?php echo $caption; ?>"
                 style="animation-delay: <?php echo $index * 0.1; ?>s;">
                
                <?php if ($isVideo) { ?>
                    <div class="position-relative">
                        <video style="width: 100%; height: auto; display: block;" muted preload="metadata">
                            <source src="<?php echo htmlspecialchars($thumbnail); ?>#t=1" type="video/mp4">
                        </video>
                        <div class="gallery-overlay">
                            <div class="gallery-overlay-content">
                                <i class="fas fa-play-circle fa-3x text-white mb-2"></i>
                                <?php if (!empty($item['title'])) { ?>
                                <h6 class="text-white mb-1"><?php echo htmlspecialchars($item['title']); ?></h6>
                                <?php } ?>
                                <small class="text-white-50">
                                    <i class="fas fa-video me-1"></i>Video
                                    <?php if ($item['duration']) { ?>
                                    • <?php echo gmdate("i:s", $item['duration']); ?>
                                    <?php } ?>
                                </small>
                            </div>
                        </div>
                    </div>
                <?php } else { ?>
                    <img src="<?php echo htmlspecialchars($thumbnail); ?>" 
                         alt="<?php echo htmlspecialchars($item['title'] ?: 'Gallery image'); ?>" 
                         loading="lazy"
                         style="width: 100%; height: auto; display: block;">
                    
                    <div class="gallery-overlay">
                        <div class="gallery-overlay-content">
                            <i class="fas fa-search-plus fa-2x text-white mb-2"></i>
                            <?php if (!empty($item['title'])) { ?>
                            <h6 class="text-white mb-1"><?php echo htmlspecialchars($item['title']); ?></h6>
                            <?php } ?>
                            <small class="text-white-50">
                                <i class="fas fa-image me-1"></i>Photo
                                <?php if ($item['width'] && $item['height']) { ?>
                                • <?php echo $item['width']; ?>×<?php echo $item['height']; ?>
                                <?php } ?>
                            </small>
                        </div>
                    </div>
                <?php } ?>
            </div>
            <?php } ?>
        </div>
        
        <!-- Gallery Info -->
        <div class="row mt-5">
            <div class="col-lg-8 mx-auto text-center">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Gallery Information</h5>
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="mb-2">
                                    <i class="fas fa-images fa-2x text-primary"></i>
                                </div>
                                <h6><?php echo count($mediaItems); ?></h6>
                                <small class="text-muted">Total Items</small>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-2">
                                    <i class="fas fa-camera fa-2x text-primary"></i>
                                </div>
                                <h6><?php echo $imageCount; ?></h6>
                                <small class="text-muted">Photos</small>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-2">
                                    <i class="fas fa-video fa-2x text-primary"></i>
                                </div>
                                <h6><?php echo $videoCount; ?></h6>
                                <small class="text-muted">Videos</small>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-2">
                                    <i class="fas fa-calendar fa-2x text-primary"></i>
                                </div>
                                <h6><?php echo date('M Y', strtotime($gallery['created_at'])); ?></h6>
                                <small class="text-muted">Created</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <?php } else { ?>
        <!-- Empty gallery message -->
        <div class="text-center py-5">
            <i class="fas fa-images fa-4x text-muted mb-4"></i>
            <h3 class="text-muted">Gallery is Empty</h3>
            <p class="text-muted">This gallery doesn't have any media items yet.</p>
            <a href="/portfolio" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Portfolio
            </a>
        </div>
        <?php } ?>
    </div>
</section>

<!-- Related Galleries -->
<?php
$relatedGalleries = $galleryModel->getAllActive();
$relatedGalleries = array_filter($relatedGalleries, function($g) use ($gallery) {
    return $g['id'] !== $gallery['id'];
});
$relatedGalleries = array_slice($relatedGalleries, 0, 3);

if (!empty($relatedGalleries)) { ?>
<section class="section bg-light">
    <div class="container">
        <h2 class="section-title animate-on-scroll">More Galleries</h2>
        <div class="row">
            <?php foreach ($relatedGalleries as $relatedGallery) { 
                $coverImage = !empty($relatedGallery['cover_image']) ? $relatedGallery['cover_image'] : 'assets/images/placeholder-gallery.jpg';
            ?>
            <div class="col-lg-4 mb-4">
                <div class="card h-100 animate-on-scroll">
                    <img src="<?php echo htmlspecialchars($coverImage); ?>" 
                         class="card-img-top" 
                         alt="<?php echo htmlspecialchars($relatedGallery['title']); ?>" 
                         style="height: 200px; object-fit: cover;">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo htmlspecialchars($relatedGallery['title']); ?></h5>
                        <p class="card-text"><?php echo htmlspecialchars(substr($relatedGallery['description'], 0, 100)) . (strlen($relatedGallery['description']) > 100 ? '...' : ''); ?></p>
                        <a href="/gallery/<?php echo htmlspecialchars($relatedGallery['slug']); ?>" class="btn btn-primary">
                            <i class="fas fa-eye me-2"></i>View Gallery
                        </a>
                    </div>
                </div>
            </div>
            <?php } ?>
        </div>
    </div>
</section>
<?php } ?>

<!-- Call to Action -->
<section class="section bg-gradient text-white overlay-dark">
    <div class="container text-center">
        <h2 class="animate-on-scroll">Interested in Similar Work?</h2>
        <p class="lead mb-4 animate-on-scroll">Contact us to discuss your photography or video project.</p>
        <div class="animate-on-scroll">
            <a href="/contact" class="btn btn-light btn-lg me-3">
                <i class="fas fa-envelope me-2"></i>Get In Touch
            </a>
            <a href="/portfolio" class="btn btn-outline-light btn-lg">
                <i class="fas fa-images me-2"></i>View All Galleries
            </a>
        </div>
    </div>
</section>

<?php includeFooter(); ?>
