<?php
$title = "Page Not Found - " . getSetting('site_title', SITE_NAME);
$description = "The page you are looking for could not be found.";
includeHeader($title, $description);
?>

<div class="container py-5">
    <div class="row justify-content-center text-center">
        <div class="col-lg-6">
            <div class="error-404">
                <h1 class="display-1 fw-bold text-primary">404</h1>
                <h2 class="h3 mb-4">Page Not Found</h2>
                <p class="lead text-muted mb-4">
                    Sorry, the page you are looking for could not be found. 
                    It may have been moved, deleted, or you entered the wrong URL.
                </p>
                
                <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>Go Home
                    </a>
                    <a href="/portfolio" class="btn btn-outline-primary">
                        <i class="fas fa-images me-2"></i>View Portfolio
                    </a>
                    <a href="/contact" class="btn btn-outline-secondary">
                        <i class="fas fa-envelope me-2"></i>Contact Us
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Work Section -->
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="text-center mb-4">Recent Work</h3>
            <div class="row">
                <?php
                $mediaModel = new MediaItem();
                $recentMedia = $mediaModel->getRecent(4);
                
                foreach ($recentMedia as $item) {
                    $thumbnail = !empty($item['thumbnail_path']) ? $item['thumbnail_path'] : $item['file_path'];
                    echo '<div class="col-md-3 mb-4">
                            <div class="card h-100">
                                <img src="' . htmlspecialchars($thumbnail) . '" class="card-img-top" alt="' . htmlspecialchars($item['title']) . '" style="height: 200px; object-fit: cover;">
                                <div class="card-body">
                                    <h6 class="card-title">' . htmlspecialchars($item['title'] ?: 'Untitled') . '</h6>
                                    <p class="card-text small text-muted">' . htmlspecialchars($item['gallery_title']) . '</p>
                                    <a href="/gallery/' . htmlspecialchars($item['gallery_slug']) . '" class="btn btn-sm btn-outline-primary">View Gallery</a>
                                </div>
                            </div>
                          </div>';
                }
                ?>
            </div>
        </div>
    </div>
</div>

<?php includeFooter(); ?>
