# Kuderik Photo - Modern Photography Portfolio

A professional photography portfolio website built with PHP, Bootstrap, MySQL, and modern web technologies. Features a responsive design, admin panel, SEO optimization, and performance enhancements.

## Features

### Frontend
- **Responsive Design**: Mobile-first approach with Bootstrap 5
- **Hero Section**: Large video/image hero with compelling call-to-action
- **Gallery System**: Beautiful masonry layouts with Fancybox lightbox
- **SEO Optimized**: Clean URLs, meta tags, structured data, and sitemap
- **Performance**: Lazy loading, image optimization, and caching
- **Contact Form**: Functional contact form with validation

### Admin Panel
- **Dashboard**: Overview of galleries, media, and messages
- **Gallery Management**: Create, edit, and organize photo galleries
- **Media Upload**: Drag-and-drop file uploads with thumbnails
- **Message Management**: View and respond to contact inquiries
- **SEO Tools**: Built-in SEO analysis and optimization tools
- **Performance Monitor**: Storage usage and optimization tools
- **Settings**: Comprehensive site configuration

### Technical Features
- **Clean URLs**: SEO-friendly routing system (/gallery/portfolio-name)
- **Database**: MySQL with proper relationships and indexes
- **Security**: Password hashing, SQL injection protection, CSRF protection
- **Caching**: Browser caching and compression via .htaccess
- **Image Optimization**: Automatic thumbnail generation and compression
- **Structured Data**: JSON-LD markup for better search visibility

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache web server with mod_rewrite
- GD extension for image processing
- PDO MySQL extension

## Installation

### 1. Download and Setup
```bash
# Clone or download the project
git clone <repository-url> kuderik-photo
cd kuderik-photo

# Set proper permissions
chmod 755 uploads/
chmod 755 uploads/images/
chmod 755 uploads/videos/
chmod 755 uploads/thumbnails/
```

### 2. Database Setup
1. Create a MySQL database named `kuderik_photo`
2. Navigate to `http://your-domain/kuderik-photo/install.php`
3. Follow the installation wizard to configure database connection
4. The installer will create all necessary tables and default data

### 3. Configuration
- Update `config/database.php` with your database credentials
- Modify `SITE_URL` constant to match your domain
- Configure email settings for contact form notifications

### 4. Admin Access
- **Default Username**: admin
- **Default Password**: admin123
- **Admin URL**: `http://your-domain/kuderik-photo/admin/`

**Important**: Change the default admin password immediately after installation!

## File Structure

```
kuderik-photo/
├── admin/                  # Admin panel
│   ├── includes/          # Admin templates
│   ├── galleries.php      # Gallery management
│   ├── media.php         # Media management
│   ├── messages.php      # Contact messages
│   ├── settings.php      # Site settings
│   └── seo.php           # SEO tools
├── assets/               # Static assets
│   ├── css/             # Stylesheets
│   ├── js/              # JavaScript files
│   └── images/          # Site images
├── config/              # Configuration files
├── includes/            # Core PHP files
├── models/              # Database models
├── pages/               # Page templates
├── uploads/             # User uploads
├── index.php           # Main entry point
├── sitemap.xml.php     # Dynamic sitemap
└── robots.txt          # Search engine instructions
```

## Usage

### Adding Content
1. **Login to Admin**: Navigate to `/admin/` and login
2. **Create Galleries**: Go to Galleries → Add Gallery
3. **Upload Media**: Go to Media Items → Upload Media
4. **Configure Settings**: Update site information in Settings

### SEO Optimization
- Use descriptive gallery titles and descriptions
- Add titles to all media items
- Configure meta keywords and descriptions
- Submit sitemap to Google Search Console
- Set up Google Analytics tracking

### Performance Tips
- Use the built-in image optimization tools
- Enable browser caching (already configured)
- Optimize images before uploading
- Monitor storage usage in Performance panel

## Customization

### Styling
- Edit `assets/css/style.css` for custom styles
- Modify color scheme by updating CSS variables
- Customize Bootstrap components as needed

### Functionality
- Add new page templates in `pages/` directory
- Extend models in `models/` directory
- Add new routes in `includes/router.php`

## Security

### Built-in Security Features
- Password hashing with PHP's `password_hash()`
- SQL injection protection via prepared statements
- XSS protection with `htmlspecialchars()`
- CSRF protection for admin forms
- File upload validation and restrictions

### Security Best Practices
1. Change default admin credentials
2. Keep PHP and MySQL updated
3. Use HTTPS in production
4. Regular security audits
5. Backup database regularly

## Testing Checklist

### Frontend Testing
- [ ] Homepage loads correctly
- [ ] All navigation links work
- [ ] Gallery pages display properly
- [ ] Fancybox lightbox functions
- [ ] Contact form submits successfully
- [ ] Responsive design on mobile/tablet
- [ ] Cross-browser compatibility

### Admin Testing
- [ ] Admin login works
- [ ] Dashboard displays statistics
- [ ] Gallery CRUD operations
- [ ] Media upload functionality
- [ ] Message management
- [ ] Settings save correctly
- [ ] SEO tools function properly

### Performance Testing
- [ ] Page load times under 3 seconds
- [ ] Images load with lazy loading
- [ ] Caching headers present
- [ ] Compressed assets served
- [ ] Mobile performance optimized

## Troubleshooting

### Common Issues

**Database Connection Error**
- Check database credentials in `config/database.php`
- Ensure MySQL service is running
- Verify database exists and user has permissions

**File Upload Issues**
- Check directory permissions (755 for uploads/)
- Verify PHP upload limits in php.ini
- Ensure GD extension is installed

**Admin Access Problems**
- Clear browser cache and cookies
- Check session configuration
- Verify admin user exists in database

**Performance Issues**
- Enable compression in .htaccess
- Optimize images using admin tools
- Check server resources and limits

## Support

For technical support or questions:
- Check the troubleshooting section above
- Review server error logs
- Ensure all requirements are met
- Verify file permissions are correct

## License

This project is licensed under the MIT License. See LICENSE file for details.

## Credits

- **Bootstrap 5**: Frontend framework
- **Fancybox**: Image/video lightbox
- **Font Awesome**: Icon library
- **Google Fonts**: Typography (Inter & Playfair Display)
