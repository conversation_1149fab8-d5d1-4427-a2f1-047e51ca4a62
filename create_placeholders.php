<?php
/**
 * Create placeholder images for the portfolio
 */

// Create placeholder images
function createPlaceholderImage($width, $height, $text, $filename) {
    $image = imagecreate($width, $height);
    
    // Colors
    $bg_color = imagecolorallocate($image, 200, 200, 200);
    $text_color = imagecolorallocate($image, 100, 100, 100);
    
    // Add text
    $font_size = 5;
    $text_width = imagefontwidth($font_size) * strlen($text);
    $text_height = imagefontheight($font_size);
    
    $x = ($width - $text_width) / 2;
    $y = ($height - $text_height) / 2;
    
    imagestring($image, $font_size, $x, $y, $text, $text_color);
    
    // Save image
    imagejpeg($image, $filename, 80);
    imagedestroy($image);
    
    echo "Created: $filename\n";
}

// Create directories if they don't exist
$dirs = [
    'assets/images',
    'uploads/images',
    'uploads/videos',
    'uploads/thumbnails'
];

foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Create placeholder images
createPlaceholderImage(800, 600, 'Gallery Cover Image', 'assets/images/placeholder-gallery.jpg');
createPlaceholderImage(1200, 800, 'About Hero Image', 'assets/images/about-hero.jpg');
createPlaceholderImage(400, 300, 'Thumbnail', 'assets/images/placeholder-thumb.jpg');

// Create favicon placeholder
createPlaceholderImage(32, 32, 'KP', 'assets/images/favicon.ico');
createPlaceholderImage(180, 180, 'KP', 'assets/images/apple-touch-icon.png');

echo "Placeholder images created successfully!\n";
?>
