<?php
/**
 * Site Settings Model
 */

class SiteSetting {
    private $db;
    private static $cache = [];
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get setting value by key
     */
    public function get($key, $default = '') {
        // Check cache first
        if (isset(self::$cache[$key])) {
            return self::$cache[$key];
        }
        
        $sql = "SELECT setting_value FROM site_settings WHERE setting_key = ?";
        $result = $this->db->fetchOne($sql, [$key]);
        
        $value = $result ? $result['setting_value'] : $default;
        
        // Cache the value
        self::$cache[$key] = $value;
        
        return $value;
    }
    
    /**
     * Set setting value
     */
    public function set($key, $value, $type = 'text', $description = '') {
        $sql = "INSERT INTO site_settings (setting_key, setting_value, setting_type, description) 
                VALUES (?, ?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value),
                setting_type = VALUES(setting_type),
                description = VALUES(description),
                updated_at = CURRENT_TIMESTAMP";
        
        $result = $this->db->query($sql, [$key, $value, $type, $description]);
        
        // Update cache
        self::$cache[$key] = $value;
        
        return $result;
    }
    
    /**
     * Get all settings
     */
    public function getAll() {
        $sql = "SELECT * FROM site_settings ORDER BY setting_key";
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get settings by type
     */
    public function getByType($type) {
        $sql = "SELECT * FROM site_settings WHERE setting_type = ? ORDER BY setting_key";
        return $this->db->fetchAll($sql, [$type]);
    }
    
    /**
     * Update multiple settings
     */
    public function updateMultiple($settings) {
        foreach ($settings as $key => $value) {
            $this->set($key, $value);
        }
        return true;
    }
    
    /**
     * Delete setting
     */
    public function delete($key) {
        $sql = "DELETE FROM site_settings WHERE setting_key = ?";
        $result = $this->db->query($sql, [$key]);
        
        // Remove from cache
        unset(self::$cache[$key]);
        
        return $result;
    }
    
    /**
     * Get grouped settings for admin
     */
    public function getGrouped() {
        $settings = $this->getAll();
        $grouped = [
            'general' => [],
            'hero' => [],
            'contact' => [],
            'social' => [],
            'seo' => [],
            'other' => []
        ];
        
        foreach ($settings as $setting) {
            $key = $setting['setting_key'];
            
            if (strpos($key, 'hero_') === 0) {
                $grouped['hero'][] = $setting;
            } elseif (strpos($key, 'contact_') === 0) {
                $grouped['contact'][] = $setting;
            } elseif (strpos($key, 'social_') === 0) {
                $grouped['social'][] = $setting;
            } elseif (in_array($key, ['meta_keywords', 'google_analytics'])) {
                $grouped['seo'][] = $setting;
            } elseif (in_array($key, ['site_title', 'site_tagline', 'about_text', 'footer_text'])) {
                $grouped['general'][] = $setting;
            } else {
                $grouped['other'][] = $setting;
            }
        }
        
        return $grouped;
    }
    
    /**
     * Clear cache
     */
    public static function clearCache() {
        self::$cache = [];
    }
}

// Helper function to get setting value globally
function getSetting($key, $default = '') {
    static $settingModel = null;
    if ($settingModel === null) {
        $settingModel = new SiteSetting();
    }
    return $settingModel->get($key, $default);
}
?>
