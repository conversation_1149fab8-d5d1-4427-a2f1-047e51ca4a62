<?php
/**
 * Gallery Model
 */

class Gallery {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get all active galleries
     */
    public function getAllActive($featured_only = false) {
        $sql = "SELECT * FROM galleries WHERE is_active = 1";
        if ($featured_only) {
            $sql .= " AND is_featured = 1";
        }
        $sql .= " ORDER BY sort_order ASC, created_at DESC";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get gallery by slug
     */
    public function getBySlug($slug) {
        $sql = "SELECT * FROM galleries WHERE slug = ? AND is_active = 1";
        return $this->db->fetchOne($sql, [$slug]);
    }
    
    /**
     * Get gallery by ID
     */
    public function getById($id) {
        $sql = "SELECT * FROM galleries WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }
    
    /**
     * Get all galleries (admin)
     */
    public function getAll() {
        $sql = "SELECT g.*, 
                       COUNT(m.id) as media_count,
                       MAX(m.created_at) as last_updated
                FROM galleries g 
                LEFT JOIN media_items m ON g.id = m.gallery_id AND m.is_active = 1
                GROUP BY g.id 
                ORDER BY g.sort_order ASC, g.created_at DESC";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Create new gallery
     */
    public function create($data) {
        $sql = "INSERT INTO galleries (title, slug, description, sort_order, is_featured, is_active) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $params = [
            $data['title'],
            $data['slug'],
            $data['description'] ?? '',
            $data['sort_order'] ?? 0,
            $data['is_featured'] ?? 0,
            $data['is_active'] ?? 1
        ];
        
        $this->db->query($sql, $params);
        return $this->db->lastInsertId();
    }
    
    /**
     * Update gallery
     */
    public function update($id, $data) {
        $sql = "UPDATE galleries SET 
                title = ?, slug = ?, description = ?, 
                sort_order = ?, is_featured = ?, is_active = ?,
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?";
        
        $params = [
            $data['title'],
            $data['slug'],
            $data['description'] ?? '',
            $data['sort_order'] ?? 0,
            $data['is_featured'] ?? 0,
            $data['is_active'] ?? 1,
            $id
        ];
        
        return $this->db->query($sql, $params);
    }
    
    /**
     * Delete gallery
     */
    public function delete($id) {
        $sql = "DELETE FROM galleries WHERE id = ?";
        return $this->db->query($sql, [$id]);
    }
    
    /**
     * Update cover image
     */
    public function updateCoverImage($id, $image_path) {
        $sql = "UPDATE galleries SET cover_image = ? WHERE id = ?";
        return $this->db->query($sql, [$image_path, $id]);
    }
    
    /**
     * Check if slug exists
     */
    public function slugExists($slug, $exclude_id = null) {
        $sql = "SELECT COUNT(*) as count FROM galleries WHERE slug = ?";
        $params = [$slug];
        
        if ($exclude_id) {
            $sql .= " AND id != ?";
            $params[] = $exclude_id;
        }
        
        $result = $this->db->fetchOne($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * Generate unique slug
     */
    public function generateUniqueSlug($title, $exclude_id = null) {
        $slug = createSlug($title);
        $original_slug = $slug;
        $counter = 1;
        
        while ($this->slugExists($slug, $exclude_id)) {
            $slug = $original_slug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
}
?>
