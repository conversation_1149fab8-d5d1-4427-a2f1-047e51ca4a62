<?php
/**
 * Media Item Model
 */

class MediaItem {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get media items by gallery ID
     */
    public function getByGalleryId($gallery_id, $active_only = true) {
        $sql = "SELECT * FROM media_items WHERE gallery_id = ?";
        $params = [$gallery_id];
        
        if ($active_only) {
            $sql .= " AND is_active = 1";
        }
        
        $sql .= " ORDER BY sort_order ASC, created_at DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get media item by ID
     */
    public function getById($id) {
        $sql = "SELECT m.*, g.title as gallery_title, g.slug as gallery_slug 
                FROM media_items m 
                LEFT JOIN galleries g ON m.gallery_id = g.id 
                WHERE m.id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }
    
    /**
     * Get all media items (admin)
     */
    public function getAll($gallery_id = null) {
        $sql = "SELECT m.*, g.title as gallery_title 
                FROM media_items m 
                LEFT JOIN galleries g ON m.gallery_id = g.id";
        $params = [];
        
        if ($gallery_id) {
            $sql .= " WHERE m.gallery_id = ?";
            $params[] = $gallery_id;
        }
        
        $sql .= " ORDER BY m.created_at DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Create new media item
     */
    public function create($data) {
        $sql = "INSERT INTO media_items 
                (gallery_id, title, description, filename, original_filename, 
                 file_path, thumbnail_path, file_type, file_size, mime_type, 
                 width, height, duration, sort_order, is_active) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $data['gallery_id'],
            $data['title'] ?? '',
            $data['description'] ?? '',
            $data['filename'],
            $data['original_filename'],
            $data['file_path'],
            $data['thumbnail_path'] ?? null,
            $data['file_type'],
            $data['file_size'] ?? 0,
            $data['mime_type'] ?? '',
            $data['width'] ?? null,
            $data['height'] ?? null,
            $data['duration'] ?? null,
            $data['sort_order'] ?? 0,
            $data['is_active'] ?? 1
        ];
        
        $this->db->query($sql, $params);
        return $this->db->lastInsertId();
    }
    
    /**
     * Update media item
     */
    public function update($id, $data) {
        $sql = "UPDATE media_items SET 
                title = ?, description = ?, sort_order = ?, is_active = ?,
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?";
        
        $params = [
            $data['title'] ?? '',
            $data['description'] ?? '',
            $data['sort_order'] ?? 0,
            $data['is_active'] ?? 1,
            $id
        ];
        
        return $this->db->query($sql, $params);
    }
    
    /**
     * Delete media item
     */
    public function delete($id) {
        // Get file paths before deletion
        $item = $this->getById($id);
        
        if ($item) {
            // Delete files
            if (file_exists($item['file_path'])) {
                unlink($item['file_path']);
            }
            if ($item['thumbnail_path'] && file_exists($item['thumbnail_path'])) {
                unlink($item['thumbnail_path']);
            }
            
            // Delete from database
            $sql = "DELETE FROM media_items WHERE id = ?";
            return $this->db->query($sql, [$id]);
        }
        
        return false;
    }
    
    /**
     * Update sort order
     */
    public function updateSortOrder($id, $sort_order) {
        $sql = "UPDATE media_items SET sort_order = ? WHERE id = ?";
        return $this->db->query($sql, [$sort_order, $id]);
    }
    
    /**
     * Get featured media items
     */
    public function getFeatured($limit = 12) {
        $sql = "SELECT m.*, g.title as gallery_title, g.slug as gallery_slug 
                FROM media_items m 
                INNER JOIN galleries g ON m.gallery_id = g.id 
                WHERE m.is_active = 1 AND g.is_active = 1 AND g.is_featured = 1
                ORDER BY m.created_at DESC 
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    /**
     * Get recent media items
     */
    public function getRecent($limit = 8) {
        $sql = "SELECT m.*, g.title as gallery_title, g.slug as gallery_slug 
                FROM media_items m 
                INNER JOIN galleries g ON m.gallery_id = g.id 
                WHERE m.is_active = 1 AND g.is_active = 1
                ORDER BY m.created_at DESC 
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
}
?>
