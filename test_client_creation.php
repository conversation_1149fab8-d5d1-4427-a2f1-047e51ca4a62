<?php
/**
 * Test Client Creation Script
 * Creates test clients to verify the system works
 */

require_once 'config/database.php';

echo "🧪 TESTING CLIENT CREATION SYSTEM!\n";
echo "==================================\n\n";

try {
    $db = Database::getInstance();
    echo "✅ Database connection successful!\n\n";
    
    // Test clients to create
    $testClients = [
        [
            'first_name' => '<PERSON>',
            'last_name' => '<PERSON>',
            'email' => '<EMAIL>',
            'phone' => '(*************',
            'address' => '123 Main Street',
            'city' => 'Los Angeles',
            'state' => 'CA',
            'zip_code' => '90210',
            'preferred_contact' => 'email',
            'referral_source' => 'Google Search'
        ],
        [
            'first_name' => '<PERSON>',
            'last_name' => '<PERSON>',
            'email' => '<EMAIL>',
            'phone' => '(*************',
            'address' => '456 Oak Avenue',
            'city' => 'San Francisco',
            'state' => 'CA',
            'zip_code' => '94102',
            'preferred_contact' => 'phone',
            'referral_source' => 'Friend Referral'
        ],
        [
            'first_name' => 'Emily',
            'last_name' => 'Rodriguez',
            'email' => '<EMAIL>',
            'phone' => '(*************',
            'address' => '789 Pine Street',
            'city' => 'San Diego',
            'state' => 'CA',
            'zip_code' => '92101',
            'preferred_contact' => 'text',
            'referral_source' => 'Instagram'
        ]
    ];
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($testClients as $client) {
        echo "👤 Creating client: {$client['first_name']} {$client['last_name']}\n";
        
        try {
            // Check if client already exists
            $existing = $db->fetchOne("SELECT id FROM users WHERE email = ?", [$client['email']]);
            if ($existing) {
                echo "  ⚠️  Client already exists, skipping...\n";
                continue;
            }
            
            // Generate random password
            $password = bin2hex(random_bytes(8));
            $passwordHash = password_hash($password, PASSWORD_DEFAULT);
            
            // Create user account
            $db->query("
                INSERT INTO users (username, email, password_hash, first_name, last_name, phone, role, status, email_verified)
                VALUES (?, ?, ?, ?, ?, ?, 'client', 'active', 1)
            ", [
                $client['email'], // Use email as username
                $client['email'],
                $passwordHash,
                $client['first_name'],
                $client['last_name'],
                $client['phone']
            ]);

            $userId = $db->getConnection()->lastInsertId();

            // Create client profile
            $db->query("
                INSERT INTO client_profiles (user_id, address_line1, city, state, zip_code, preferred_contact, referral_source)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ", [
                $userId,
                $client['address'],
                $client['city'],
                $client['state'],
                $client['zip_code'],
                $client['preferred_contact'],
                $client['referral_source']
            ]);
            
            echo "  ✅ Client created successfully!\n";
            echo "  📧 Email: {$client['email']}\n";
            echo "  🔑 Password: $password\n";
            echo "  🆔 User ID: $userId\n\n";
            
            $successCount++;
            
        } catch (Exception $e) {
            echo "  ❌ Error creating client: " . $e->getMessage() . "\n\n";
            $errorCount++;
        }
    }
    
    echo "📊 CLIENT CREATION SUMMARY:\n";
    echo "===========================\n";
    echo "✅ Successfully created: $successCount clients\n";
    echo "❌ Errors: $errorCount\n\n";
    
    // Verify clients were created
    echo "🔍 VERIFYING CREATED CLIENTS:\n";
    echo "============================\n";
    
    $clients = $db->fetchAll("
        SELECT u.*, cp.city, cp.state, cp.preferred_contact, cp.referral_source
        FROM users u
        LEFT JOIN client_profiles cp ON u.id = cp.user_id
        WHERE u.role = 'client'
        ORDER BY u.created_at DESC
    ");
    
    foreach ($clients as $client) {
        echo "👤 {$client['first_name']} {$client['last_name']}\n";
        echo "   📧 {$client['email']}\n";
        echo "   📞 {$client['phone']}\n";
        echo "   📍 {$client['city']}, {$client['state']}\n";
        echo "   💬 Prefers: {$client['preferred_contact']}\n";
        echo "   🔗 Found via: {$client['referral_source']}\n";
        echo "   📅 Joined: " . date('M j, Y', strtotime($client['created_at'])) . "\n\n";
    }
    
    echo "🎯 NEXT STEPS:\n";
    echo "=============\n";
    echo "1. ✅ Client creation system working!\n";
    echo "2. 🔄 Test client login with created accounts\n";
    echo "3. 🔄 Create test bookings for clients\n";
    echo "4. 🔄 Test photo upload and gallery assignment\n";
    echo "5. 🔄 Test order creation and tracking\n\n";
    
    echo "🚀 PHASE 2 CLIENT CREATION COMPLETE!\n";
    
} catch (Exception $e) {
    echo "❌ TEST FAILED!\n";
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Client Creation Test Complete!\n";
echo str_repeat("=", 50) . "\n";
?>
