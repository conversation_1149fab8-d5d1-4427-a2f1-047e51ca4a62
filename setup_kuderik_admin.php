<?php
/**
 * Setup Admin User for Kuderik.com
 */

require_once 'config/database.php';

echo "🔧 SETTING UP ADMIN FOR KUDERIK.COM!\n";
echo "====================================\n\n";

try {
    $db = Database::getInstance();
    
    // Check current admin user
    $currentAdmin = $db->fetchOne("SELECT * FROM users WHERE role = 'admin' LIMIT 1");
    
    if ($currentAdmin) {
        echo "📋 Current Admin User:\n";
        echo "   ID: {$currentAdmin['id']}\n";
        echo "   Email: {$currentAdmin['email']}\n";
        echo "   Username: {$currentAdmin['username']}\n";
        echo "   Name: {$currentAdmin['first_name']} {$currentAdmin['last_name']}\n\n";
    }
    
    // Update admin user for kuderik.com
    $kuderikEmail = '<EMAIL>';
    $kuderikPassword = 'kuderik2024';  // Strong password for kuderik
    $passwordHash = password_hash($kuderikPassword, PASSWORD_DEFAULT);
    
    if ($currentAdmin) {
        // Update existing admin
        $db->query("
            UPDATE users 
            SET email = ?, username = ?, password_hash = ?, first_name = ?, last_name = ?, updated_at = NOW()
            WHERE id = ?
        ", [
            $kuderikEmail,
            'kuderik_admin',
            $passwordHash,
            'Kuderik',
            'Admin',
            $currentAdmin['id']
        ]);
        
        echo "✅ Updated existing admin user\n";
    } else {
        // Create new admin
        $db->query("
            INSERT INTO users (username, email, password_hash, first_name, last_name, role, status, email_verified) 
            VALUES (?, ?, ?, ?, ?, 'admin', 'active', 1)
        ", [
            'kuderik_admin',
            $kuderikEmail,
            $passwordHash,
            'Kuderik',
            'Admin'
        ]);
        
        echo "✅ Created new admin user\n";
    }
    
    // Verify the updated admin
    $updatedAdmin = $db->fetchOne("SELECT * FROM users WHERE email = ? AND role = 'admin'", [$kuderikEmail]);
    
    if ($updatedAdmin && password_verify($kuderikPassword, $updatedAdmin['password_hash'])) {
        echo "✅ Admin user verification successful!\n\n";
        
        echo "🔑 KUDERIK.COM ADMIN CREDENTIALS:\n";
        echo "=================================\n";
        echo "Email: $kuderikEmail\n";
        echo "Password: $kuderikPassword\n";
        echo "URL: http://localhost:8000/admin/login.php\n\n";
        
        echo "🌐 SITE CONFIGURATION:\n";
        echo "======================\n";
        
        // Update site settings for kuderik.com
        $siteSettings = [
            'site_title' => 'Kuderik Photography',
            'site_description' => 'Professional Photography Services by Kuderik',
            'site_url' => 'https://kuderik.com',
            'admin_email' => $kuderikEmail,
            'company_name' => 'Kuderik Photography',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+****************'
        ];
        
        foreach ($siteSettings as $key => $value) {
            // Check if setting exists
            $existing = $db->fetchOne("SELECT * FROM system_settings WHERE setting_key = ?", [$key]);
            
            if ($existing) {
                $db->query("UPDATE system_settings SET setting_value = ? WHERE setting_key = ?", [$value, $key]);
            } else {
                $db->query("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?)", [$key, $value]);
            }
            
            echo "✅ Updated setting: $key = $value\n";
        }
        
        echo "\n🎯 SETUP COMPLETE!\n";
        echo "==================\n";
        echo "✅ Admin user configured for kuderik.com\n";
        echo "✅ Site settings updated\n";
        echo "✅ Login system ready\n\n";
        
        echo "🚀 NEXT STEPS:\n";
        echo "==============\n";
        echo "1. Go to: http://localhost:8000/admin/login.php\n";
        echo "2. Enter email: $kuderikEmail\n";
        echo "3. Enter password: $kuderikPassword\n";
        echo "4. Access all admin features\n\n";
        
    } else {
        echo "❌ Admin user verification failed!\n";
    }
    
} catch (Exception $e) {
    echo "❌ SETUP FAILED!\n";
    echo "Error: " . $e->getMessage() . "\n";
}

echo "🎊 Kuderik Admin Setup Complete!\n";
?>
