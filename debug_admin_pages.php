<?php
/**
 * Debug Admin Pages
 */

echo "🔍 DEBUGGING ADMIN PAGES!\n";
echo "========================\n\n";

// Test 1: Check if we can include the database config
echo "🧪 Test 1: Database Config\n";
try {
    require_once 'config/database.php';
    echo "✅ Database config loaded successfully\n";
    
    $db = Database::getInstance();
    echo "✅ Database connection successful\n";
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 2: Check if we can include functions
echo "🧪 Test 2: Functions File\n";
try {
    require_once 'includes/functions.php';
    echo "✅ Functions file loaded successfully\n";
    
    if (function_exists('getSetting')) {
        echo "✅ getSetting function exists\n";
        $siteName = getSetting('site_title', 'Default Site');
        echo "✅ getSetting test: $siteName\n";
    } else {
        echo "❌ getSetting function not found\n";
    }
} catch (Exception $e) {
    echo "❌ Functions error: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 3: Check admin includes
echo "🧪 Test 3: Admin Includes\n";
try {
    if (file_exists('admin/includes/header.php')) {
        echo "✅ Admin header file exists\n";
    } else {
        echo "❌ Admin header file not found\n";
    }
    
    if (file_exists('admin/includes/sidebar.php')) {
        echo "✅ Admin sidebar file exists\n";
    } else {
        echo "❌ Admin sidebar file not found\n";
    }
} catch (Exception $e) {
    echo "❌ Admin includes error: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 4: Simulate session and test upload-photos.php logic
echo "🧪 Test 4: Upload Photos Logic\n";
try {
    session_start();
    $_SESSION['admin_logged_in'] = true;
    $_SESSION['admin_user_id'] = 1;
    
    // Test the isLoggedIn function
    if (function_exists('isLoggedIn')) {
        $loggedIn = isLoggedIn();
        echo "✅ isLoggedIn function result: " . ($loggedIn ? 'true' : 'false') . "\n";
    } else {
        echo "❌ isLoggedIn function not found\n";
    }
    
    // Test gallery query
    $galleries = $db->fetchAll("SELECT id, title FROM galleries ORDER BY title ASC");
    echo "✅ Gallery query successful, found " . count($galleries) . " galleries\n";
    
} catch (Exception $e) {
    echo "❌ Upload photos logic error: " . $e->getMessage() . "\n";
}
echo "\n";

echo "🎯 DEBUG COMPLETE!\n";
echo "==================\n";
echo "If all tests pass, the admin pages should work.\n";
echo "Try logging in at: http://localhost:8000/admin/login.php\n";
echo "Email: <EMAIL>\n";
echo "Password: admin123\n";
?>
