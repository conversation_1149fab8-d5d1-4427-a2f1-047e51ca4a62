/* <PERSON><PERSON>ik Photo - Custom Styles */

/* CSS Variables */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #34495e;
    --accent-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --text-color: #2c3e50;
    --text-muted: #7f8c8d;
    --border-color: #bdc3c7;
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
    --transition: all 0.3s ease;
    --border-radius: 8px;
    --font-primary: 'Inter', sans-serif;
    --font-heading: 'Playfair Display', serif;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--text-color);
    background-color: #fff;
    padding-top: 76px; /* Account for fixed navbar */
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

.display-1, .display-2, .display-3 {
    font-family: var(--font-heading);
}

/* Navigation Styles */
.navbar {
    background: rgba(44, 62, 80, 0.95) !important;
    backdrop-filter: blur(10px);
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.navbar-brand {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--accent-color) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: var(--transition);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 80%;
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.hero-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 2rem;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-cta {
    margin-top: 2rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.75rem 2rem;
    transition: var(--transition);
    border: none;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
    box-shadow: var(--shadow-light);
}

.btn-primary:hover {
    background: #c0392b;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    color: white;
}

.btn-outline-primary {
    border: 2px solid var(--accent-color);
    color: var(--accent-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--accent-color);
    color: white;
    transform: translateY(-2px);
}

.btn-lg {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.card-img-top {
    transition: var(--transition);
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* Gallery Grid */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.gallery-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    cursor: pointer;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.gallery-item img,
.gallery-item video {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: var(--transition);
}

.gallery-item:hover img,
.gallery-item:hover video {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay-content {
    text-align: center;
    color: white;
    padding: 1rem;
}

/* Masonry Grid for Portfolio */
.masonry-grid {
    column-count: 3;
    column-gap: 1.5rem;
    margin: 2rem 0;
}

.masonry-item {
    break-inside: avoid;
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    cursor: pointer;
}

.masonry-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.masonry-item img {
    width: 100%;
    height: auto;
    display: block;
    transition: var(--transition);
}

.masonry-item:hover img {
    transform: scale(1.05);
}

/* Responsive Masonry */
@media (max-width: 992px) {
    .masonry-grid {
        column-count: 2;
    }
}

@media (max-width: 576px) {
    .masonry-grid {
        column-count: 1;
    }
}

/* Section Styles */
.section {
    padding: 5rem 0;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--accent-color);
}

/* Footer */
footer {
    background: var(--dark-color) !important;
    color: white !important;
}

footer * {
    color: white !important;
}

footer .text-muted {
    color: rgba(255, 255, 255, 0.7) !important;
}

footer .social-links a {
    transition: var(--transition);
    color: white !important;
}

footer .social-links a:hover {
    color: var(--accent-color) !important;
    transform: translateY(-2px);
}

footer a {
    color: rgba(255, 255, 255, 0.8) !important;
    text-decoration: none;
}

footer a:hover {
    color: white !important;
}

/* Back to Top Button */
#backToTop {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--accent-color);
    border: none;
    color: white;
    font-size: 1.2rem;
    transition: var(--transition);
    z-index: 1000;
}

#backToTop:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .section {
        padding: 3rem 0;
    }
}

/* Utility Classes */
.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.overlay-dark {
    position: relative;
}

.overlay-dark::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
}

.overlay-dark > * {
    position: relative;
    z-index: 2;
}

/* Performance Optimizations */

/* Enhanced lazy loading */
.lazy {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    will-change: opacity;
}

.lazy.loaded {
    opacity: 1;
    will-change: auto;
}

/* Optimize animations */
.card, .gallery-item, .masonry-item {
    will-change: transform;
}

.card:hover, .gallery-item:hover, .masonry-item:hover {
    will-change: auto;
}

/* Optimize images */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* WebP support optimization */
.webp img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

/* Reduce layout shifts */
.gallery-grid, .masonry-grid {
    contain: layout style paint;
}

/* Critical resource hints */
.hero-video {
    preload: metadata;
}

/* Font optimization */
@font-face {
    font-display: swap;
}

/* Smooth scrolling performance */
html {
    scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }

    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Clean Fancybox styling - minimal interface */
.fancybox__toolbar {
    background: rgba(0, 0, 0, 0.8) !important;
}

.fancybox__button {
    background: transparent !important;
    border: none !important;
    color: white !important;
}

.fancybox__button:hover {
    background: rgba(255, 255, 255, 0.1) !important;
}

/* Hide counter and other UI elements */
.fancybox__counter {
    display: none !important;
}

.fancybox__infobar {
    display: none !important;
}

/* Clean close button */
.fancybox__button--close {
    top: 20px !important;
    right: 20px !important;
    width: 40px !important;
    height: 40px !important;
    background: rgba(0, 0, 0, 0.5) !important;
    border-radius: 50% !important;
}

.fancybox__button--close:hover {
    background: rgba(0, 0, 0, 0.8) !important;
}
