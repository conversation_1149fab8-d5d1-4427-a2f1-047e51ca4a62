/* <PERSON><PERSON>ik Photo - Custom Styles */

/* CSS Variables */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #34495e;
    --accent-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --text-color: #2c3e50;
    --text-muted: #7f8c8d;
    --border-color: #bdc3c7;
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
    --transition: all 0.3s ease;
    --border-radius: 8px;
    --font-primary: 'Inter', sans-serif;
    --font-heading: 'Playfair Display', serif;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--text-color);
    background-color: #fff;
    padding-top: 76px; /* Account for fixed navbar */
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

.display-1, .display-2, .display-3 {
    font-family: var(--font-heading);
}

/* Navigation Styles */
.navbar {
    background: rgba(44, 62, 80, 0.95) !important;
    backdrop-filter: blur(10px);
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.navbar-brand {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--accent-color) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: var(--transition);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 80%;
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.hero-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 2rem;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-cta {
    margin-top: 2rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.75rem 2rem;
    transition: var(--transition);
    border: none;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
    box-shadow: var(--shadow-light);
}

.btn-primary:hover {
    background: #c0392b;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    color: white;
}

.btn-outline-primary {
    border: 2px solid var(--accent-color);
    color: var(--accent-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--accent-color);
    color: white;
    transform: translateY(-2px);
}

.btn-lg {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.card-img-top {
    transition: var(--transition);
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* Gallery Grid */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.gallery-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    cursor: pointer;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.gallery-item img,
.gallery-item video {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: var(--transition);
}

.gallery-item:hover img,
.gallery-item:hover video {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay-content {
    text-align: center;
    color: white;
    padding: 1rem;
}

/* Masonry Grid for Portfolio */
.masonry-grid {
    column-count: 3;
    column-gap: 1.5rem;
    margin: 2rem 0;
}

.masonry-item {
    break-inside: avoid;
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    cursor: pointer;
}

.masonry-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.masonry-item img {
    width: 100%;
    height: auto;
    display: block;
    transition: var(--transition);
}

.masonry-item:hover img {
    transform: scale(1.05);
}

/* Simple Featured Gallery Styles */
.featured-image-container {
    position: relative;
    display: inline-block;
    margin: 2rem 0;
}

.featured-gallery-link {
    position: relative;
    display: block;
    text-decoration: none;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.featured-gallery-link:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
}

.featured-image {
    transition: transform 0.3s ease;
    cursor: pointer;
}

.featured-gallery-link:hover .featured-image {
    transform: scale(1.02);
}

.featured-overlay-simple {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    backdrop-filter: blur(1px);
}

.featured-gallery-link:hover .featured-overlay-simple {
    opacity: 1;
}

.featured-overlay-content {
    text-align: center;
    padding: 2rem;
    transform: translateY(10px);
    transition: transform 0.3s ease;
}

.featured-gallery-link:hover .featured-overlay-content {
    transform: translateY(0);
}

.featured-overlay-content i {
    margin-bottom: 1rem;
    opacity: 0.9;
}

/* Legacy Featured Gallery Styles */
.featured-gallery-container {
    margin: 2rem 0;
}

.featured-image-wrapper {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.4s ease;
    background: #f8f9fa;
}

.featured-image-wrapper:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.3);
}

.featured-media {
    width: 100%;
    height: auto;
    display: block;
    max-height: 70vh;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.featured-image-wrapper:hover .featured-media {
    transform: scale(1.05);
}

.featured-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
    backdrop-filter: blur(2px);
}

.featured-image-wrapper:hover .featured-overlay {
    opacity: 1;
}

.featured-image-wrapper:hover .featured-overlay-content {
    transform: translateY(0);
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Gallery Hint Section */
.gallery-hint {
    margin-top: 3rem;
    padding: 2rem;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.gallery-thumbnails {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.gallery-thumb {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    opacity: 0.8;
}

.gallery-thumb:hover {
    transform: scale(1.1);
    opacity: 1;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.gallery-thumb-more {
    width: 60px;
    height: 60px;
    background: var(--accent-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Responsive Masonry */
@media (max-width: 992px) {
    .masonry-grid {
        column-count: 2;
    }
}

@media (max-width: 768px) {
    .featured-media {
        max-height: 50vh;
    }

    .featured-overlay-content {
        padding: 1.5rem;
    }

    .featured-overlay-content h4 {
        font-size: 1.25rem;
    }

    .gallery-hint {
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .gallery-thumb {
        width: 50px;
        height: 50px;
    }

    .gallery-thumb-more {
        width: 50px;
        height: 50px;
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .masonry-grid {
        column-count: 1;
    }

    .featured-overlay-content i {
        font-size: 2.5rem !important;
    }
}

/* Section Styles */
.section {
    padding: 5rem 0;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--accent-color);
}

/* Modern Professional Footer */
.modern-footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.modern-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

/* Brand Section */
.footer-brand .brand-title {
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    letter-spacing: -0.5px;
}

.footer-brand .brand-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* Modern Social Links */
.social-links-modern {
    display: flex;
    gap: 12px;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.1);
    color: white !important;
    border-radius: 12px;
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    color: white !important;
}

.social-link i {
    font-size: 1.1rem;
}

/* Footer Sections */
.footer-section .section-title {
    color: white;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.footer-section .section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--accent-color);
    border-radius: 1px;
}

/* Footer Links */
.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8) !important;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
}

.footer-links a:hover {
    color: white !important;
    padding-left: 8px;
}

.footer-links a::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 1px;
    background: var(--accent-color);
    transition: width 0.3s ease;
}

.footer-links a:hover::before {
    width: 4px;
}

/* Modern Contact Info */
.contact-info-modern .contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 12px;
}

.contact-info-modern .contact-item i {
    color: var(--accent-color);
    font-size: 1rem;
    margin-top: 2px;
    min-width: 16px;
}

.contact-info-modern .contact-item a,
.contact-info-modern .contact-item span {
    color: rgba(255, 255, 255, 0.8) !important;
    text-decoration: none;
    font-size: 0.9rem;
    line-height: 1.5;
    transition: color 0.3s ease;
}

.contact-info-modern .contact-item a:hover {
    color: white !important;
}

/* Footer Bottom */
.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.5rem 0;
    margin-top: 2rem;
}

.footer-bottom .copyright {
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
    font-size: 0.9rem;
}

.footer-bottom-links {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 12px;
    font-size: 0.9rem;
}

.footer-bottom-links .admin-link {
    color: rgba(255, 255, 255, 0.8) !important;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-bottom-links .admin-link:hover {
    color: var(--accent-color) !important;
}

.footer-bottom-links .separator {
    color: rgba(255, 255, 255, 0.4);
}

.footer-bottom-links .powered-by {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
}

/* Responsive Footer */
@media (max-width: 768px) {
    .modern-footer {
        text-align: center;
    }

    .footer-section .section-title::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .footer-bottom-links {
        justify-content: center;
        margin-top: 1rem;
    }

    .social-links-modern {
        justify-content: center;
    }
}

/* Back to Top Button */
#backToTop {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--accent-color);
    border: none;
    color: white;
    font-size: 1.2rem;
    transition: var(--transition);
    z-index: 1000;
}

#backToTop:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .section {
        padding: 3rem 0;
    }
}

/* Utility Classes */
.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.overlay-dark {
    position: relative;
}

.overlay-dark::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
}

.overlay-dark > * {
    position: relative;
    z-index: 2;
}

/* Performance Optimizations */

/* Enhanced lazy loading */
.lazy {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    will-change: opacity;
}

.lazy.loaded {
    opacity: 1;
    will-change: auto;
}

/* Optimize animations */
.card, .gallery-item, .masonry-item {
    will-change: transform;
}

.card:hover, .gallery-item:hover, .masonry-item:hover {
    will-change: auto;
}

/* Optimize images */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* WebP support optimization */
.webp img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

/* Reduce layout shifts */
.gallery-grid, .masonry-grid {
    contain: layout style paint;
}

/* Critical resource hints */
.hero-video {
    preload: metadata;
}

/* Font optimization */
@font-face {
    font-display: swap;
}

/* Smooth scrolling performance */
html {
    scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }

    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Clean Fancybox styling - minimal interface */
.fancybox__toolbar {
    background: rgba(0, 0, 0, 0.8) !important;
}

.fancybox__button {
    background: transparent !important;
    border: none !important;
    color: white !important;
}

.fancybox__button:hover {
    background: rgba(255, 255, 255, 0.1) !important;
}

/* Hide counter and other UI elements */
.fancybox__counter {
    display: none !important;
}

.fancybox__infobar {
    display: none !important;
}

/* Clean close button */
.fancybox__button--close {
    top: 20px !important;
    right: 20px !important;
    width: 40px !important;
    height: 40px !important;
    background: rgba(0, 0, 0, 0.5) !important;
    border-radius: 50% !important;
}

.fancybox__button--close:hover {
    background: rgba(0, 0, 0, 0.8) !important;
}
