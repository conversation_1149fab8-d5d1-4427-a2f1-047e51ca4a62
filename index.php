<?php
/**
 * <PERSON><PERSON><PERSON> Photo - Modern Photography Portfolio
 * Main entry point with SEO-friendly routing
 */

// Start session
session_start();

// Include main configuration
require_once 'config.php';

// Include configuration and core files
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/router.php';

// Get the requested page from URL
$request_uri = $_SERVER['REQUEST_URI'];
$script_name = $_SERVER['SCRIPT_NAME'];

// Get the base path (directory where index.php is located)
$base_path = dirname($script_name);
if ($base_path === '/') {
    $base_path = '';
}

// Remove base path from request URI
$request_path = $request_uri;
if (!empty($base_path)) {
    $request_path = str_replace($base_path, '', $request_uri);
}
$request_path = trim($request_path, '/');

// Remove query string if present
if (($pos = strpos($request_path, '?')) !== false) {
    $request_path = substr($request_path, 0, $pos);
}

// Default to home if empty
if (empty($request_path)) {
    $request_path = 'home';
}

// Initialize router
$router = new Router();

// Define routes
$router->addRoute('home', 'pages/home.php');
$router->addRoute('about', 'pages/about.php');
$router->addRoute('portfolio', 'pages/portfolio.php');
$router->addRoute('gallery/([a-zA-Z0-9-]+)', 'pages/gallery.php');
$router->addRoute('contact', 'pages/contact.php');
$router->addRoute('services', 'pages/services.php');

// Handle the route
$router->handleRequest($request_path);
?>
