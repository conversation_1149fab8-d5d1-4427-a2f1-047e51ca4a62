<?php
/**
 * Add new photography galleries to existing database
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

try {
    $db = Database::getInstance();
    
    echo "Adding new photography galleries...\n";
    
    // New galleries to add
    $newGalleries = [
        [
            'title' => 'Food Photography',
            'slug' => 'food-photography',
            'description' => 'Mouth-watering food photography for restaurants and culinary brands',
            'is_featured' => 1
        ],
        [
            'title' => 'Product Photography',
            'slug' => 'product-photography', 
            'description' => 'High-quality product shots for e-commerce and marketing',
            'is_featured' => 1
        ],
        [
            'title' => '360 Tours',
            'slug' => '360-tours',
            'description' => 'Immersive 360-degree virtual tours for businesses and real estate',
            'is_featured' => 1
        ],
        [
            'title' => 'Real Estate Photography',
            'slug' => 'real-estate-photography',
            'description' => 'Professional real estate photography showcasing properties at their best',
            'is_featured' => 1
        ]
    ];
    
    $added = 0;
    $skipped = 0;
    
    foreach ($newGalleries as $gallery) {
        // Check if gallery already exists
        $existing = $db->fetchOne(
            "SELECT id FROM galleries WHERE slug = ?",
            [$gallery['slug']]
        );
        
        if (!$existing) {
            // Add new gallery
            $db->query(
                "INSERT INTO galleries (title, slug, description, is_featured, is_active, sort_order) VALUES (?, ?, ?, ?, 1, 0)",
                [$gallery['title'], $gallery['slug'], $gallery['description'], $gallery['is_featured']]
            );
            echo "✅ Added: " . $gallery['title'] . "\n";
            $added++;
        } else {
            echo "⏭️  Skipped: " . $gallery['title'] . " (already exists)\n";
            $skipped++;
        }
    }
    
    echo "\n🎉 Gallery update complete!\n";
    echo "Added: $added galleries\n";
    echo "Skipped: $skipped galleries\n";
    echo "\nYou can now view the new galleries at:\n";
    echo "- http://localhost:8000/portfolio\n";
    echo "- http://localhost:8000/admin/galleries.php\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
