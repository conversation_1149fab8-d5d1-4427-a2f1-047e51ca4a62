<?php
/**
 * Client Login Page
 */

session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Redirect if already logged in
if (isset($_SESSION['client_logged_in']) && $_SESSION['client_logged_in'] === true) {
    header('Location: client-portal.php');
    exit;
}

$db = Database::getInstance();
$message = '';
$messageType = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        $message = "Please enter both email and password.";
        $messageType = 'danger';
    } else {
        // Check user credentials
        $user = $db->fetchOne("
            SELECT id, email, password_hash, first_name, last_name, status 
            FROM users 
            WHERE email = ? AND role = 'client'
        ", [$email]);
        
        if ($user && password_verify($password, $user['password_hash'])) {
            if ($user['status'] === 'active') {
                // Login successful
                $_SESSION['client_logged_in'] = true;
                $_SESSION['client_id'] = $user['id'];
                $_SESSION['client_name'] = $user['first_name'] . ' ' . $user['last_name'];
                
                // Update last login
                $db->query("UPDATE users SET last_login = NOW() WHERE id = ?", [$user['id']]);
                
                // Log activity
                $db->query("
                    INSERT INTO activity_log (user_id, action, entity_type, ip_address, user_agent) 
                    VALUES (?, 'client_login', 'user', ?, ?)
                ", [
                    $user['id'],
                    $_SERVER['REMOTE_ADDR'] ?? '',
                    $_SERVER['HTTP_USER_AGENT'] ?? ''
                ]);
                
                header('Location: client-portal.php');
                exit;
            } else {
                $message = "Your account is not active. Please contact support.";
                $messageType = 'warning';
            }
        } else {
            $message = "Invalid email or password.";
            $messageType = 'danger';
        }
    }
}

$title = "Client Login - " . getSetting('site_title', SITE_NAME);
$description = "Access your private galleries and manage your orders.";
includeHeader($title, $description);
?>

<!-- Client Login Section -->
<section class="section min-vh-100 d-flex align-items-center bg-gradient">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <h3 class="mb-0">
                            <i class="fas fa-user-lock me-2"></i>Client Login
                        </h3>
                        <p class="mb-0 mt-2 opacity-75">Access your private galleries</p>
                    </div>
                    
                    <div class="card-body p-5">
                        <?php if ($message) { ?>
                        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                            <?php echo htmlspecialchars($message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php } ?>
                        
                        <form method="POST" id="loginForm">
                            <div class="mb-4">
                                <label for="email" class="form-label">Email Address</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                           required autocomplete="email">
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           required autocomplete="current-password">
                                    <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                    <label class="form-check-label" for="remember">
                                        Remember me
                                    </label>
                                </div>
                            </div>
                            
                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center">
                            <a href="forgot-password.php" class="text-decoration-none">
                                <i class="fas fa-question-circle me-1"></i>Forgot your password?
                            </a>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <p class="text-muted mb-2">Don't have an account?</p>
                            <a href="/contact/" class="btn btn-outline-primary">
                                <i class="fas fa-envelope me-1"></i>Contact Us
                            </a>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-light text-center py-3">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt me-1"></i>
                            Your privacy and security are our top priority
                        </small>
                    </div>
                </div>
                
                <!-- Help Section -->
                <div class="card mt-4 border-0 bg-transparent">
                    <div class="card-body text-center">
                        <h6 class="text-white mb-3">Need Help?</h6>
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <a href="/contact/" class="btn btn-outline-light btn-sm w-100">
                                    <i class="fas fa-envelope me-1"></i>Contact Support
                                </a>
                            </div>
                            <div class="col-md-6 mb-2">
                                <a href="/" class="btn btn-outline-light btn-sm w-100">
                                    <i class="fas fa-home me-1"></i>Back to Website
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.min-vh-100 {
    min-height: 100vh;
}

.card {
    border-radius: 15px;
    overflow: hidden;
}

.input-group-text {
    background: #f8f9fa;
    border-right: none;
}

.form-control {
    border-left: none;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: none;
}

.input-group:focus-within .input-group-text {
    border-color: #007bff;
    background: #e3f2fd;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.border-left-primary {
    border-left: 4px solid #007bff !important;
}

@media (max-width: 768px) {
    .card-body {
        padding: 2rem !important;
    }
}
</style>

<script>
// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordInput = document.getElementById('password');
    const icon = this.querySelector('i');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// Form validation
document.getElementById('loginForm').addEventListener('submit', function(e) {
    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value;
    
    if (!email || !password) {
        e.preventDefault();
        alert('Please fill in all fields.');
        return false;
    }
    
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        e.preventDefault();
        alert('Please enter a valid email address.');
        return false;
    }
});

// Auto-focus on email field
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('email').focus();
});
</script>

<?php includeFooter(); ?>
