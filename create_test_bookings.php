<?php
/**
 * Create Test Bookings for System Testing
 */

require_once 'config/database.php';

echo "📅 CREATING TEST BOOKINGS!\n";
echo "==========================\n\n";

try {
    $db = Database::getInstance();
    echo "✅ Database connection successful!\n\n";
    
    // Get client IDs
    $clients = $db->fetchAll("SELECT id, first_name, last_name, email FROM users WHERE role = 'client'");
    
    if (empty($clients)) {
        echo "❌ No clients found! Please run test_client_creation.php first.\n";
        exit;
    }
    
    echo "👥 Found " . count($clients) . " clients for booking creation\n\n";
    
    // Test bookings to create
    $testBookings = [
        [
            'client_id' => $clients[0]['id'], // <PERSON>
            'session_type' => 'portrait',
            'package_type' => 'Professional',
            'session_date' => '2025-08-15',
            'session_time' => '10:00:00',
            'duration_hours' => 2.0,
            'location' => 'Studio Downtown',
            'total_amount' => 349.00,
            'deposit_amount' => 100.00,
            'special_requests' => 'Natural lighting preferred, outdoor shots if weather permits'
        ],
        [
            'client_id' => $clients[1]['id'], // Michael Chen
            'session_type' => 'wedding',
            'package_type' => 'Classic',
            'session_date' => '2025-09-20',
            'session_time' => '14:00:00',
            'duration_hours' => 8.0,
            'location' => 'Sunset Gardens Venue',
            'total_amount' => 2499.00,
            'deposit_amount' => 500.00,
            'special_requests' => 'Include engagement session, need second photographer for ceremony'
        ],
        [
            'client_id' => $clients[2]['id'], // Emily Rodriguez
            'session_type' => 'event',
            'package_type' => 'Standard',
            'session_date' => '2025-08-30',
            'session_time' => '18:00:00',
            'duration_hours' => 4.0,
            'location' => 'Corporate Center Ballroom',
            'total_amount' => 699.00,
            'deposit_amount' => 200.00,
            'special_requests' => 'Corporate event, need photos for marketing materials'
        ]
    ];
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($testBookings as $index => $booking) {
        $client = $clients[$index];
        echo "📅 Creating booking for: {$client['first_name']} {$client['last_name']}\n";
        
        try {
            // Calculate balance due
            $balanceDue = $booking['total_amount'] - $booking['deposit_amount'];
            
            // Create booking
            $db->query("
                INSERT INTO bookings (client_id, session_type, package_type, session_date, session_time, 
                                    duration_hours, location, total_amount, deposit_amount, balance_due, 
                                    special_requests, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')
            ", [
                $booking['client_id'],
                $booking['session_type'],
                $booking['package_type'],
                $booking['session_date'],
                $booking['session_time'],
                $booking['duration_hours'],
                $booking['location'],
                $booking['total_amount'],
                $booking['deposit_amount'],
                $balanceDue,
                $booking['special_requests']
            ]);
            
            $bookingId = $db->getConnection()->lastInsertId();
            
            echo "  ✅ Booking created successfully!\n";
            echo "  🆔 Booking ID: $bookingId\n";
            echo "  📸 Session: {$booking['session_type']} - {$booking['package_type']}\n";
            echo "  📅 Date: {$booking['session_date']} at {$booking['session_time']}\n";
            echo "  💰 Total: $" . number_format($booking['total_amount'], 2) . "\n";
            echo "  💳 Deposit: $" . number_format($booking['deposit_amount'], 2) . "\n";
            echo "  💵 Balance: $" . number_format($balanceDue, 2) . "\n\n";
            
            $successCount++;
            
        } catch (Exception $e) {
            echo "  ❌ Error creating booking: " . $e->getMessage() . "\n\n";
            $errorCount++;
        }
    }
    
    echo "📊 BOOKING CREATION SUMMARY:\n";
    echo "============================\n";
    echo "✅ Successfully created: $successCount bookings\n";
    echo "❌ Errors: $errorCount\n\n";
    
    // Verify bookings were created
    echo "🔍 VERIFYING CREATED BOOKINGS:\n";
    echo "==============================\n";
    
    $bookings = $db->fetchAll("
        SELECT b.*, u.first_name, u.last_name, u.email 
        FROM bookings b 
        JOIN users u ON b.client_id = u.id 
        ORDER BY b.session_date ASC
    ");
    
    foreach ($bookings as $booking) {
        echo "📅 {$booking['first_name']} {$booking['last_name']} - " . ucfirst($booking['session_type']) . "\n";
        echo "   📧 {$booking['email']}\n";
        echo "   📦 Package: {$booking['package_type']}\n";
        echo "   📅 Date: " . date('M j, Y', strtotime($booking['session_date'])) . " at " . date('g:i A', strtotime($booking['session_time'])) . "\n";
        echo "   ⏱️  Duration: {$booking['duration_hours']} hours\n";
        echo "   📍 Location: {$booking['location']}\n";
        echo "   💰 Total: $" . number_format($booking['total_amount'], 2) . "\n";
        echo "   🔄 Status: " . ucfirst($booking['status']) . "\n";
        echo "   📝 Notes: {$booking['special_requests']}\n\n";
    }
    
    // Calculate statistics
    $totalRevenue = array_sum(array_column($bookings, 'total_amount'));
    $pendingBookings = count(array_filter($bookings, fn($b) => $b['status'] === 'pending'));
    
    echo "📊 BOOKING STATISTICS:\n";
    echo "=====================\n";
    echo "📅 Total Bookings: " . count($bookings) . "\n";
    echo "⏳ Pending Bookings: $pendingBookings\n";
    echo "💰 Total Revenue: $" . number_format($totalRevenue, 2) . "\n";
    echo "📈 Average Booking: $" . number_format($totalRevenue / count($bookings), 2) . "\n\n";
    
    echo "🎯 NEXT STEPS:\n";
    echo "=============\n";
    echo "1. ✅ Test bookings created!\n";
    echo "2. 🔄 Test booking status updates\n";
    echo "3. 🔄 Test booking calendar view\n";
    echo "4. 🔄 Create galleries for bookings\n";
    echo "5. 🔄 Test order creation from bookings\n\n";
    
    echo "🚀 PHASE 4 BOOKING CREATION COMPLETE!\n";
    
} catch (Exception $e) {
    echo "❌ TEST FAILED!\n";
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Booking Creation Test Complete!\n";
echo str_repeat("=", 50) . "\n";
?>
