<?php
/**
 * Reset Admin Password
 */

require_once 'config/database.php';

echo "🔑 RESETTING ADMIN PASSWORD!\n";
echo "============================\n\n";

try {
    $db = Database::getInstance();
    
    $newPassword = 'admin123';
    $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // Update admin password
    $result = $db->query("
        UPDATE users 
        SET password_hash = ? 
        WHERE role = 'admin' AND email = '<EMAIL>'
    ", [$passwordHash]);
    
    echo "✅ Admin password reset successfully!\n\n";
    
    // Verify the new password works
    $admin = $db->fetchOne("
        SELECT id, email, password_hash, first_name, last_name 
        FROM users 
        WHERE email = '<EMAIL>' AND role = 'admin'
    ");
    
    if ($admin && password_verify($newPassword, $admin['password_hash'])) {
        echo "✅ Password verification test passed!\n";
        echo "✅ Admin login is now ready\n\n";
        
        echo "🔑 ADMIN LOGIN CREDENTIALS:\n";
        echo "===========================\n";
        echo "Email: <EMAIL>\n";
        echo "Password: admin123\n";
        echo "URL: http://localhost:8000/admin/login.php\n\n";
        
        echo "🎯 NOW YOU CAN:\n";
        echo "===============\n";
        echo "1. ✅ Log into admin panel\n";
        echo "2. ✅ Access upload-photos.php\n";
        echo "3. ✅ Access bookings.php\n";
        echo "4. ✅ Access all admin features\n\n";
        
    } else {
        echo "❌ Password verification still failed!\n";
    }
    
} catch (Exception $e) {
    echo "❌ RESET FAILED!\n";
    echo "Error: " . $e->getMessage() . "\n";
}

echo "🚀 Admin Password Reset Complete!\n";
?>
