<?php
/**
 * Core Functions
 */

// Include model classes (with path checking)
$modelsPath = __DIR__ . '/../models/';
if (file_exists($modelsPath . 'Gallery.php')) {
    require_once $modelsPath . 'Gallery.php';
}
if (file_exists($modelsPath . 'MediaItem.php')) {
    require_once $modelsPath . 'MediaItem.php';
}
if (file_exists($modelsPath . 'SiteSetting.php')) {
    require_once $modelsPath . 'SiteSetting.php';
}

/**
 * Sanitize input data
 */
function sanitize($data) {
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Generate secure password hash
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

/**
 * Redirect to login if not authenticated
 */
function requireAuth() {
    if (!isLoggedIn()) {
        header('Location: /admin/login.php');
        exit;
    }
}

/**
 * Generate SEO-friendly slug
 */
function createSlug($text) {
    $text = strtolower($text);
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    return trim($text, '-');
}

/**
 * Format file size
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * Get file extension
 */
function getFileExtension($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

/**
 * Check if file is image
 */
function isImage($filename) {
    $ext = getFileExtension($filename);
    return in_array($ext, ALLOWED_IMAGE_TYPES);
}

/**
 * Check if file is video
 */
function isVideo($filename) {
    $ext = getFileExtension($filename);
    return in_array($ext, ALLOWED_VIDEO_TYPES);
}

/**
 * Generate thumbnail
 */
function generateThumbnail($source, $destination, $width = THUMB_WIDTH, $height = THUMB_HEIGHT) {
    $imageInfo = getimagesize($source);
    if (!$imageInfo) return false;
    
    $sourceWidth = $imageInfo[0];
    $sourceHeight = $imageInfo[1];
    $mimeType = $imageInfo['mime'];
    
    // Create source image
    switch ($mimeType) {
        case 'image/jpeg':
            $sourceImage = imagecreatefromjpeg($source);
            break;
        case 'image/png':
            $sourceImage = imagecreatefrompng($source);
            break;
        case 'image/gif':
            $sourceImage = imagecreatefromgif($source);
            break;
        case 'image/webp':
            $sourceImage = imagecreatefromwebp($source);
            break;
        default:
            return false;
    }
    
    // Calculate dimensions
    $ratio = min($width / $sourceWidth, $height / $sourceHeight);
    $newWidth = round($sourceWidth * $ratio);
    $newHeight = round($sourceHeight * $ratio);
    
    // Create thumbnail
    $thumbnail = imagecreatetruecolor($newWidth, $newHeight);
    
    // Preserve transparency for PNG and GIF
    if ($mimeType == 'image/png' || $mimeType == 'image/gif') {
        imagealphablending($thumbnail, false);
        imagesavealpha($thumbnail, true);
        $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
        imagefilledrectangle($thumbnail, 0, 0, $newWidth, $newHeight, $transparent);
    }
    
    // Resize image
    imagecopyresampled($thumbnail, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $sourceWidth, $sourceHeight);
    
    // Save thumbnail
    $result = imagejpeg($thumbnail, $destination, THUMB_QUALITY);
    
    // Clean up
    imagedestroy($sourceImage);
    imagedestroy($thumbnail);
    
    return $result;
}

/**
 * Get page title
 */
function getPageTitle($page = '') {
    $title = SITE_NAME;
    if (!empty($page)) {
        $title = ucfirst(str_replace('-', ' ', $page)) . ' - ' . $title;
    }
    return $title;
}

/**
 * Include header
 */
function includeHeader($title = '', $description = '', $keywords = '') {
    include 'includes/header.php';
}

/**
 * Include footer
 */
function includeFooter() {
    include 'includes/footer.php';
}

/**
 * Generate structured data (JSON-LD) for SEO
 */
function generateStructuredData($type = 'website', $data = []) {
    $baseUrl = getSetting('site_url', SITE_URL);
    $siteName = getSetting('site_title', SITE_NAME);
    $siteDescription = getSetting('site_tagline', SITE_DESCRIPTION);

    $structuredData = [
        '@context' => 'https://schema.org',
        '@type' => $type
    ];

    switch ($type) {
        case 'website':
            $structuredData = array_merge($structuredData, [
                'name' => $siteName,
                'description' => $siteDescription,
                'url' => $baseUrl,
                'potentialAction' => [
                    '@type' => 'SearchAction',
                    'target' => $baseUrl . '/portfolio?search={search_term_string}',
                    'query-input' => 'required name=search_term_string'
                ]
            ]);
            break;

        case 'organization':
            $structuredData = array_merge($structuredData, [
                'name' => $siteName,
                'description' => $siteDescription,
                'url' => $baseUrl,
                'logo' => $baseUrl . '/assets/images/logo.png',
                'contactPoint' => [
                    '@type' => 'ContactPoint',
                    'telephone' => getSetting('contact_phone'),
                    'contactType' => 'customer service',
                    'email' => getSetting('contact_email')
                ],
                'address' => [
                    '@type' => 'PostalAddress',
                    'streetAddress' => getSetting('contact_address')
                ],
                'sameAs' => array_filter([
                    getSetting('social_facebook'),
                    getSetting('social_instagram'),
                    getSetting('social_twitter'),
                    getSetting('social_youtube')
                ])
            ]);
            break;

        case 'imageGallery':
            $structuredData = array_merge($structuredData, [
                'name' => $data['title'] ?? '',
                'description' => $data['description'] ?? '',
                'url' => $baseUrl . '/gallery/' . ($data['slug'] ?? ''),
                'image' => $data['images'] ?? []
            ]);
            break;

        case 'photograph':
            $structuredData = array_merge($structuredData, [
                'name' => $data['title'] ?? '',
                'description' => $data['description'] ?? '',
                'contentUrl' => $baseUrl . '/' . ($data['file_path'] ?? ''),
                'thumbnailUrl' => $baseUrl . '/' . ($data['thumbnail_path'] ?? ''),
                'creator' => [
                    '@type' => 'Person',
                    'name' => $siteName
                ]
            ]);
            break;
    }

    return '<script type="application/ld+json">' . json_encode($structuredData, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . '</script>';
}

/**
 * Generate Open Graph meta tags
 */
function generateOpenGraphTags($title = '', $description = '', $image = '', $url = '') {
    $siteName = getSetting('site_title', SITE_NAME);
    $baseUrl = getSetting('site_url', SITE_URL);

    $title = $title ?: $siteName;
    $description = $description ?: getSetting('site_tagline', SITE_DESCRIPTION);
    $url = $url ?: $baseUrl;
    $image = $image ?: $baseUrl . '/assets/images/og-default.jpg';

    $tags = [
        'og:title' => $title,
        'og:description' => $description,
        'og:type' => 'website',
        'og:url' => $url,
        'og:site_name' => $siteName,
        'og:image' => $image,
        'twitter:card' => 'summary_large_image',
        'twitter:title' => $title,
        'twitter:description' => $description,
        'twitter:image' => $image
    ];

    $output = '';
    foreach ($tags as $property => $content) {
        if (!empty($content)) {
            $prefix = strpos($property, 'twitter:') === 0 ? 'name' : 'property';
            $output .= '<meta ' . $prefix . '="' . htmlspecialchars($property) . '" content="' . htmlspecialchars($content) . '">' . "\n";
        }
    }

    return $output;
}
?>
