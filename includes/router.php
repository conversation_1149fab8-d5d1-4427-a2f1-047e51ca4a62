<?php
/**
 * Simple Router Class for SEO-friendly URLs
 */

class Router {
    private $routes = [];
    
    public function addRoute($pattern, $file) {
        $this->routes[$pattern] = $file;
    }
    
    public function handleRequest($requestPath) {
        foreach ($this->routes as $pattern => $file) {
            // Convert pattern to regex
            $regex = '#^' . str_replace('([a-zA-Z0-9-]+)', '(?P<param>[a-zA-Z0-9-]+)', $pattern) . '$#';

            if (preg_match($regex, $requestPath, $matches)) {
                // Extract parameters
                $params = [];
                foreach ($matches as $key => $value) {
                    if (is_string($key)) {
                        $params[$key] = $value;
                    }
                }

                // Set global variables for the page
                $GLOBALS['route_params'] = $params;
                $GLOBALS['current_page'] = $requestPath;

                // Include the file
                if (file_exists($file)) {
                    include $file;
                    return;
                } else {
                    $this->show404();
                    return;
                }
            }
        }

        // No route found
        $this->show404();
    }
    
    private function show404() {
        http_response_code(404);
        include 'pages/404.php';
    }
}
?>
