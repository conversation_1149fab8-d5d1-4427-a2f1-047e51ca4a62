    </main>
    
    <!-- Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3"><?php echo getSetting('site_title', SITE_NAME); ?></h5>
                    <p class="text-muted"><?php echo getSetting('site_tagline', SITE_DESCRIPTION); ?></p>
                    
                    <!-- Social Media Links -->
                    <div class="social-links">
                        <?php
                        $social_links = [
                            'instagram' => ['icon' => 'fab fa-instagram', 'label' => 'Instagram'],
                            'facebook' => ['icon' => 'fab fa-facebook', 'label' => 'Facebook'],
                            'twitter' => ['icon' => 'fab fa-twitter', 'label' => 'Twitter'],
                            'youtube' => ['icon' => 'fab fa-youtube', 'label' => 'YouTube']
                        ];
                        
                        foreach ($social_links as $platform => $info) {
                            $url = getSetting('social_' . $platform);
                            if (!empty($url)) {
                                echo '<a href="' . htmlspecialchars($url) . '" class="text-light me-3" target="_blank" rel="noopener" aria-label="' . $info['label'] . '">
                                        <i class="' . $info['icon'] . ' fa-lg"></i>
                                      </a>';
                            }
                        }
                        ?>
                    </div>
                </div>
                
                <div class="col-lg-3 mb-4">
                    <h6 class="fw-bold mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="/" class="text-muted text-decoration-none">Home</a></li>
                        <li><a href="/about" class="text-muted text-decoration-none">About</a></li>
                        <li><a href="/portfolio" class="text-muted text-decoration-none">Portfolio</a></li>
                        <li><a href="/services" class="text-muted text-decoration-none">Services</a></li>
                        <li><a href="/contact" class="text-muted text-decoration-none">Contact</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 mb-4">
                    <h6 class="fw-bold mb-3">Galleries</h6>
                    <ul class="list-unstyled">
                        <?php
                        $galleryModel = new Gallery();
                        $galleries = $galleryModel->getAllActive(true); // Featured galleries only
                        foreach (array_slice($galleries, 0, 5) as $gallery) {
                            echo '<li><a href="/gallery/' . htmlspecialchars($gallery['slug']) . '" class="text-muted text-decoration-none">' . htmlspecialchars($gallery['title']) . '</a></li>';
                        }
                        ?>
                    </ul>
                </div>
                
                <div class="col-lg-3 mb-4">
                    <h6 class="fw-bold mb-3">Contact Info</h6>
                    <div class="contact-info">
                        <?php
                        $email = getSetting('contact_email');
                        $phone = getSetting('contact_phone');
                        $address = getSetting('contact_address');
                        
                        if (!empty($email)) {
                            echo '<p class="text-muted mb-2">
                                    <i class="fas fa-envelope me-2"></i>
                                    <a href="mailto:' . htmlspecialchars($email) . '" class="text-muted text-decoration-none">' . htmlspecialchars($email) . '</a>
                                  </p>';
                        }
                        
                        if (!empty($phone)) {
                            echo '<p class="text-muted mb-2">
                                    <i class="fas fa-phone me-2"></i>
                                    <a href="tel:' . htmlspecialchars(preg_replace('/[^0-9+]/', '', $phone)) . '" class="text-muted text-decoration-none">' . htmlspecialchars($phone) . '</a>
                                  </p>';
                        }
                        
                        if (!empty($address)) {
                            echo '<p class="text-muted mb-2">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    ' . nl2br(htmlspecialchars($address)) . '
                                  </p>';
                        }
                        ?>
                    </div>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0"><?php echo getSetting('footer_text', '© 2024 ' . SITE_NAME . '. All rights reserved.'); ?></p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="/admin" class="text-muted text-decoration-none small">Admin</a>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Back to Top Button -->
    <button id="backToTop" class="btn btn-primary position-fixed bottom-0 end-0 m-4 rounded-circle" style="display: none; z-index: 1000;">
        <i class="fas fa-arrow-up"></i>
    </button>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Fancybox JS -->
    <script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.umd.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
</body>
</html>
