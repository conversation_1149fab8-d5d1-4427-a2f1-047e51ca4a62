    </main>
    
    <!-- Modern Professional Footer -->
    <footer class="modern-footer">
        <div class="container">
            <!-- Main Footer Content -->
            <div class="row py-5">
                <!-- Brand Section -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="footer-brand">
                        <h4 class="brand-title mb-3"><?php echo getSetting('site_title', SITE_NAME); ?></h4>
                        <p class="brand-description mb-4"><?php echo getSetting('site_tagline', SITE_DESCRIPTION); ?></p>

                        <!-- Social Media Links -->
                        <div class="social-links-modern">
                            <?php
                            $social_links = [
                                'instagram' => ['icon' => 'fab fa-instagram', 'label' => 'Instagram', 'color' => '#E4405F'],
                                'facebook' => ['icon' => 'fab fa-facebook', 'label' => 'Facebook', 'color' => '#1877F2'],
                                'twitter' => ['icon' => 'fab fa-twitter', 'label' => 'Twitter', 'color' => '#1DA1F2'],
                                'youtube' => ['icon' => 'fab fa-youtube', 'label' => 'YouTube', 'color' => '#FF0000']
                            ];

                            foreach ($social_links as $platform => $info) {
                                $url = getSetting('social_' . $platform);
                                if (!empty($url)) {
                                    echo '<a href="' . htmlspecialchars($url) . '" class="social-link" target="_blank" rel="noopener" aria-label="' . $info['label'] . '" data-color="' . $info['color'] . '">
                                            <i class="' . $info['icon'] . '"></i>
                                          </a>';
                                }
                            }
                            ?>
                        </div>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="footer-section">
                        <h6 class="section-title">Navigation</h6>
                        <ul class="footer-links">
                            <li><a href="/">Home</a></li>
                            <li><a href="/about/">About</a></li>
                            <li><a href="/portfolio/">Portfolio</a></li>
                            <li><a href="/services/">Services</a></li>
                            <li><a href="/contact/">Contact</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Galleries -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="footer-section">
                        <h6 class="section-title">Featured Galleries</h6>
                        <ul class="footer-links">
                            <?php
                            $galleryModel = new Gallery();
                            $galleries = $galleryModel->getAllActive(true); // Featured galleries only
                            foreach (array_slice($galleries, 0, 6) as $gallery) {
                                echo '<li><a href="/gallery/' . htmlspecialchars($gallery['slug']) . '/">' . htmlspecialchars($gallery['title']) . '</a></li>';
                            }
                            ?>
                        </ul>
                    </div>
                </div>

                <!-- Contact Info -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="footer-section">
                        <h6 class="section-title">Get In Touch</h6>
                        <div class="contact-info-modern">
                            <?php
                            $email = getSetting('contact_email');
                            $phone = getSetting('contact_phone');
                            $address = getSetting('contact_address');

                            if (!empty($email)) {
                                echo '<div class="contact-item">
                                        <i class="fas fa-envelope"></i>
                                        <a href="mailto:' . htmlspecialchars($email) . '">' . htmlspecialchars($email) . '</a>
                                      </div>';
                            }

                            if (!empty($phone)) {
                                echo '<div class="contact-item">
                                        <i class="fas fa-phone"></i>
                                        <a href="tel:' . htmlspecialchars(preg_replace('/[^0-9+]/', '', $phone)) . '">' . htmlspecialchars($phone) . '</a>
                                      </div>';
                            }

                            if (!empty($address)) {
                                echo '<div class="contact-item">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span>' . nl2br(htmlspecialchars($address)) . '</span>
                                      </div>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer Bottom -->
            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="copyright"><?php echo getSetting('footer_text', '© 2024 ' . SITE_NAME . '. All rights reserved.'); ?></p>
                    </div>
                    <div class="col-md-6">
                        <div class="footer-bottom-links">
                            <a href="/admin/" class="admin-link">Admin Portal</a>
                            <span class="separator">•</span>
                            <span class="powered-by">Powered by Kuderik</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Back to Top Button -->
    <button id="backToTop" class="btn btn-primary position-fixed bottom-0 end-0 m-4 rounded-circle" style="display: none; z-index: 1000;">
        <i class="fas fa-arrow-up"></i>
    </button>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Fancybox JS -->
    <script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.umd.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
</body>
</html>
