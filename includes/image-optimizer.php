<?php
/**
 * Image Optimization Utilities
 */

class ImageOptimizer {
    
    /**
     * Optimize image for web
     */
    public static function optimizeImage($sourcePath, $destinationPath = null, $quality = 85, $maxWidth = 1920, $maxHeight = 1080) {
        if (!file_exists($sourcePath)) {
            return false;
        }
        
        $destinationPath = $destinationPath ?: $sourcePath;
        $imageInfo = getimagesize($sourcePath);
        
        if (!$imageInfo) {
            return false;
        }
        
        $sourceWidth = $imageInfo[0];
        $sourceHeight = $imageInfo[1];
        $mimeType = $imageInfo['mime'];
        
        // Calculate new dimensions
        $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight, 1);
        $newWidth = round($sourceWidth * $ratio);
        $newHeight = round($sourceHeight * $ratio);
        
        // Skip if no resizing needed and quality is high
        if ($ratio >= 1 && $quality >= 90) {
            return true;
        }
        
        // Create source image
        switch ($mimeType) {
            case 'image/jpeg':
                $sourceImage = imagecreatefromjpeg($sourcePath);
                break;
            case 'image/png':
                $sourceImage = imagecreatefrompng($sourcePath);
                break;
            case 'image/gif':
                $sourceImage = imagecreatefromgif($sourcePath);
                break;
            case 'image/webp':
                $sourceImage = imagecreatefromwebp($sourcePath);
                break;
            default:
                return false;
        }
        
        if (!$sourceImage) {
            return false;
        }
        
        // Create optimized image
        $optimizedImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG and GIF
        if ($mimeType == 'image/png' || $mimeType == 'image/gif') {
            imagealphablending($optimizedImage, false);
            imagesavealpha($optimizedImage, true);
            $transparent = imagecolorallocatealpha($optimizedImage, 255, 255, 255, 127);
            imagefilledrectangle($optimizedImage, 0, 0, $newWidth, $newHeight, $transparent);
        }
        
        // Resize image
        imagecopyresampled($optimizedImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $sourceWidth, $sourceHeight);
        
        // Save optimized image
        $result = false;
        switch ($mimeType) {
            case 'image/jpeg':
                $result = imagejpeg($optimizedImage, $destinationPath, $quality);
                break;
            case 'image/png':
                // PNG quality is 0-9, convert from 0-100
                $pngQuality = round((100 - $quality) / 10);
                $result = imagepng($optimizedImage, $destinationPath, $pngQuality);
                break;
            case 'image/gif':
                $result = imagegif($optimizedImage, $destinationPath);
                break;
            case 'image/webp':
                $result = imagewebp($optimizedImage, $destinationPath, $quality);
                break;
        }
        
        // Clean up
        imagedestroy($sourceImage);
        imagedestroy($optimizedImage);
        
        return $result;
    }
    
    /**
     * Generate WebP version of image
     */
    public static function generateWebP($sourcePath, $quality = 85) {
        if (!function_exists('imagewebp')) {
            return false;
        }
        
        $pathInfo = pathinfo($sourcePath);
        $webpPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.webp';
        
        $imageInfo = getimagesize($sourcePath);
        if (!$imageInfo) {
            return false;
        }
        
        $mimeType = $imageInfo['mime'];
        
        // Create source image
        switch ($mimeType) {
            case 'image/jpeg':
                $sourceImage = imagecreatefromjpeg($sourcePath);
                break;
            case 'image/png':
                $sourceImage = imagecreatefrompng($sourcePath);
                break;
            case 'image/gif':
                $sourceImage = imagecreatefromgif($sourcePath);
                break;
            default:
                return false;
        }
        
        if (!$sourceImage) {
            return false;
        }
        
        // Convert to WebP
        $result = imagewebp($sourceImage, $webpPath, $quality);
        imagedestroy($sourceImage);
        
        return $result ? $webpPath : false;
    }
    
    /**
     * Get image file size reduction percentage
     */
    public static function getCompressionRatio($originalPath, $optimizedPath) {
        if (!file_exists($originalPath) || !file_exists($optimizedPath)) {
            return 0;
        }
        
        $originalSize = filesize($originalPath);
        $optimizedSize = filesize($optimizedPath);
        
        if ($originalSize == 0) {
            return 0;
        }
        
        return round((($originalSize - $optimizedSize) / $originalSize) * 100, 2);
    }
    
    /**
     * Batch optimize images in directory
     */
    public static function batchOptimize($directory, $quality = 85, $maxWidth = 1920, $maxHeight = 1080) {
        $results = [];
        $extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $extension = strtolower($file->getExtension());
                
                if (in_array($extension, $extensions)) {
                    $filePath = $file->getPathname();
                    $originalSize = filesize($filePath);
                    
                    $success = self::optimizeImage($filePath, null, $quality, $maxWidth, $maxHeight);
                    
                    if ($success) {
                        $newSize = filesize($filePath);
                        $savings = $originalSize - $newSize;
                        $percentage = self::getCompressionRatio($filePath, $filePath);
                        
                        $results[] = [
                            'file' => $filePath,
                            'original_size' => $originalSize,
                            'new_size' => $newSize,
                            'savings' => $savings,
                            'percentage' => $percentage
                        ];
                    }
                }
            }
        }
        
        return $results;
    }
    
    /**
     * Check if WebP is supported
     */
    public static function isWebPSupported() {
        return function_exists('imagewebp');
    }
    
    /**
     * Get image dimensions without loading full image
     */
    public static function getImageDimensions($imagePath) {
        $imageInfo = getimagesize($imagePath);
        return $imageInfo ? ['width' => $imageInfo[0], 'height' => $imageInfo[1]] : false;
    }
}
?>
