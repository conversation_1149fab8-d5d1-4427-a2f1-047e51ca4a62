<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- SEO Meta Tags -->
    <title><?php echo isset($title) && !empty($title) ? htmlspecialchars($title) : getPageTitle($GLOBALS['current_page'] ?? ''); ?></title>
    <meta name="description" content="<?php echo isset($description) && !empty($description) ? htmlspecialchars($description) : getSetting('site_tagline', SITE_DESCRIPTION); ?>">
    <meta name="keywords" content="<?php echo isset($keywords) && !empty($keywords) ? htmlspecialchars($keywords) : getSetting('meta_keywords', 'photography, video production, professional photographer'); ?>">
    <meta name="author" content="<?php echo getSetting('site_title', SITE_NAME); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo isset($title) && !empty($title) ? htmlspecialchars($title) : getPageTitle($GLOBALS['current_page'] ?? ''); ?>">
    <meta property="og:description" content="<?php echo isset($description) && !empty($description) ? htmlspecialchars($description) : getSetting('site_tagline', SITE_DESCRIPTION); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL . '/' . ($GLOBALS['current_page'] ?? ''); ?>">
    <meta property="og:site_name" content="<?php echo getSetting('site_title', SITE_NAME); ?>">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo isset($title) && !empty($title) ? htmlspecialchars($title) : getPageTitle($GLOBALS['current_page'] ?? ''); ?>">
    <meta name="twitter:description" content="<?php echo isset($description) && !empty($description) ? htmlspecialchars($description) : getSetting('site_tagline', SITE_DESCRIPTION); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    <link rel="apple-touch-icon" href="assets/images/apple-touch-icon.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Fancybox CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Structured Data -->
    <?php
    // Generate structured data based on current page
    $currentPage = $GLOBALS['current_page'] ?? 'home';

    if ($currentPage === 'home') {
        echo generateStructuredData('website');
        echo generateStructuredData('organization');
    } elseif (strpos($currentPage, 'gallery/') === 0 && isset($GLOBALS['route_params']['param'])) {
        // Gallery page structured data will be added in the gallery page itself
    }
    ?>

    <!-- Google Analytics -->
    <?php
    $analytics = getSetting('google_analytics');
    if (!empty($analytics)) {
        echo $analytics;
    }
    ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <?php echo getSetting('site_title', SITE_NAME); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($GLOBALS['current_page'] ?? '') == 'home' ? 'active' : ''; ?>" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($GLOBALS['current_page'] ?? '') == 'about' ? 'active' : ''; ?>" href="/about/">About</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle <?php echo strpos($GLOBALS['current_page'] ?? '', 'portfolio') === 0 || strpos($GLOBALS['current_page'] ?? '', 'gallery') === 0 ? 'active' : ''; ?>" href="#" role="button" data-bs-toggle="dropdown">
                            Portfolio
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/portfolio">All Galleries</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <?php
                            $galleryModel = new Gallery();
                            $galleries = $galleryModel->getAllActive();
                            foreach ($galleries as $gallery) {
                                echo '<li><a class="dropdown-item" href="/gallery/' . htmlspecialchars($gallery['slug']) . '">' . htmlspecialchars($gallery['title']) . '</a></li>';
                            }
                            ?>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($GLOBALS['current_page'] ?? '') == 'services' ? 'active' : ''; ?>" href="/services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($GLOBALS['current_page'] ?? '') == 'contact' ? 'active' : ''; ?>" href="/contact">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="main-content">
