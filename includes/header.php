<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- SEO Meta Tags -->
    <title><?php echo isset($title) && !empty($title) ? htmlspecialchars($title) : getPageTitle($GLOBALS['current_page'] ?? ''); ?></title>
    <meta name="description" content="<?php echo isset($description) && !empty($description) ? htmlspecialchars($description) : getSetting('site_tagline', SITE_DESCRIPTION); ?>">
    <meta name="keywords" content="<?php echo isset($keywords) && !empty($keywords) ? htmlspecialchars($keywords) : getSetting('meta_keywords', 'photography, video production, professional photographer'); ?>">
    <meta name="author" content="<?php echo getSetting('site_title', SITE_NAME); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo isset($title) && !empty($title) ? htmlspecialchars($title) : getPageTitle($GLOBALS['current_page'] ?? ''); ?>">
    <meta property="og:description" content="<?php echo isset($description) && !empty($description) ? htmlspecialchars($description) : getSetting('site_tagline', SITE_DESCRIPTION); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL . '/' . ($GLOBALS['current_page'] ?? ''); ?>">
    <meta property="og:site_name" content="<?php echo getSetting('site_title', SITE_NAME); ?>">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo isset($title) && !empty($title) ? htmlspecialchars($title) : getPageTitle($GLOBALS['current_page'] ?? ''); ?>">
    <meta name="twitter:description" content="<?php echo isset($description) && !empty($description) ? htmlspecialchars($description) : getSetting('site_tagline', SITE_DESCRIPTION); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    <link rel="apple-touch-icon" href="/assets/images/apple-touch-icon.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Fancybox CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/assets/css/style.css">
    
    <!-- Structured Data -->
    <?php
    // Generate structured data based on current page
    $currentPage = $GLOBALS['current_page'] ?? 'home';

    if ($currentPage === 'home') {
        echo generateStructuredData('website');
        echo generateStructuredData('organization');
    } elseif (strpos($currentPage, 'gallery/') === 0 && isset($GLOBALS['route_params']['param'])) {
        // Gallery page structured data will be added in the gallery page itself
    }
    ?>

    <!-- Google Analytics -->
    <?php
    $analytics = getSetting('google_analytics');
    if (!empty($analytics)) {
        echo $analytics;
    }
    ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <?php echo getSetting('site_title', SITE_NAME); ?>
            </a>

            <!-- Hamburger Menu Button -->
            <button class="hamburger-menu" id="hamburgerBtn" aria-label="Open Menu">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>
        </div>
    </nav>

    <!-- Full Screen Menu Overlay -->
    <div class="fullscreen-menu" id="fullscreenMenu">
        <div class="fullscreen-menu-content">
            <div class="container">
                <!-- Close Button -->
                <button class="menu-close" id="menuCloseBtn" aria-label="Close Menu">
                    <i class="fas fa-times"></i>
                </button>

                <!-- Menu Title -->
                <div class="menu-header">
                    <h2 class="menu-title">Navigation</h2>
                    <p class="menu-subtitle">Explore Kuderik Photo</p>
                </div>

                <!-- Navigation Cards -->
                <div class="menu-cards">
                    <div class="row g-4">
                        <!-- Row 1 -->
                        <div class="col-lg-4 col-md-6">
                            <a href="/" class="menu-card <?php echo ($GLOBALS['current_page'] ?? '') == 'home' ? 'active' : ''; ?>">
                                <div class="menu-card-icon">
                                    <i class="fas fa-home"></i>
                                </div>
                                <h3 class="menu-card-title">Home</h3>
                                <p class="menu-card-description">Welcome & Featured Work</p>
                            </a>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <a href="/about/" class="menu-card <?php echo ($GLOBALS['current_page'] ?? '') == 'about' ? 'active' : ''; ?>">
                                <div class="menu-card-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                                <h3 class="menu-card-title">About</h3>
                                <p class="menu-card-description">My Story & Equipment</p>
                            </a>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <a href="/portfolio/" class="menu-card <?php echo strpos($GLOBALS['current_page'] ?? '', 'portfolio') === 0 || strpos($GLOBALS['current_page'] ?? '', 'gallery') === 0 ? 'active' : ''; ?>">
                                <div class="menu-card-icon">
                                    <i class="fas fa-images"></i>
                                </div>
                                <h3 class="menu-card-title">Portfolio</h3>
                                <p class="menu-card-description">Photo & Video Galleries</p>
                            </a>
                        </div>

                        <!-- Row 2 -->
                        <div class="col-lg-4 col-md-6">
                            <a href="/services/" class="menu-card <?php echo ($GLOBALS['current_page'] ?? '') == 'services' ? 'active' : ''; ?>">
                                <div class="menu-card-icon">
                                    <i class="fas fa-camera"></i>
                                </div>
                                <h3 class="menu-card-title">Services</h3>
                                <p class="menu-card-description">Photography & Video</p>
                            </a>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <a href="/contact/" class="menu-card <?php echo ($GLOBALS['current_page'] ?? '') == 'contact' ? 'active' : ''; ?>">
                                <div class="menu-card-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <h3 class="menu-card-title">Contact</h3>
                                <p class="menu-card-description">Get In Touch</p>
                            </a>
                        </div>
                        <div class="col-lg-4 col-md-6">
                            <a href="/admin/" class="menu-card admin-card">
                                <div class="menu-card-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <h3 class="menu-card-title">Admin</h3>
                                <p class="menu-card-description">Manage Content</p>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Featured Galleries Quick Access -->
                <div class="menu-galleries">
                    <h4 class="galleries-title">Quick Gallery Access</h4>
                    <div class="gallery-links">
                        <?php
                        $galleryModel = new Gallery();
                        $galleries = $galleryModel->getAllActive();
                        foreach (array_slice($galleries, 0, 6) as $gallery) {
                            echo '<a href="/gallery/' . htmlspecialchars($gallery['slug']) . '/" class="gallery-link">' . htmlspecialchars($gallery['title']) . '</a>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <main class="main-content">
