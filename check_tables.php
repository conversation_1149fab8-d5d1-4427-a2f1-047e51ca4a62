<?php
require_once 'config/database.php';

try {
    $db = Database::getInstance();
    
    echo "USERS TABLE STRUCTURE:\n";
    echo "=====================\n";
    $result = $db->fetchAll('DESCRIBE users');
    foreach ($result as $row) {
        echo "  {$row['Field']} - {$row['Type']}\n";
    }
    
    echo "\nCLIENT_PROFILES TABLE STRUCTURE:\n";
    echo "================================\n";
    $result = $db->fetchAll('DESCRIBE client_profiles');
    foreach ($result as $row) {
        echo "  {$row['Field']} - {$row['Type']}\n";
    }

    echo "\nGALLERIES TABLE STRUCTURE:\n";
    echo "=========================\n";
    $result = $db->fetchAll('DESCRIBE galleries');
    foreach ($result as $row) {
        echo "  {$row['Field']} - {$row['Type']}\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
