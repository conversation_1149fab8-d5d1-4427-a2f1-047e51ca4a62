<?php
/**
 * Client Portal - Private Gallery Access and Order Management
 */

session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if client is logged in
if (!isset($_SESSION['client_logged_in']) || $_SESSION['client_logged_in'] !== true) {
    header('Location: client-login.php');
    exit;
}

$db = Database::getInstance();
$clientId = $_SESSION['client_id'];

// Get client information
$client = $db->fetchOne("
    SELECT u.*, cp.phone, cp.address_line1, cp.city, cp.state 
    FROM users u 
    LEFT JOIN client_profiles cp ON u.id = cp.user_id 
    WHERE u.id = ? AND u.role = 'client'
", [$clientId]);

if (!$client) {
    session_destroy();
    header('Location: client-login.php');
    exit;
}

// Get client's galleries
$galleries = $db->fetchAll("
    SELECT g.*, COUNT(mi.id) as photo_count 
    FROM galleries g 
    LEFT JOIN gallery_permissions gp ON g.id = gp.gallery_id 
    LEFT JOIN media_items mi ON g.id = mi.gallery_id AND mi.is_active = 1
    WHERE (g.client_id = ? OR gp.client_id = ?) AND g.is_active = 1
    GROUP BY g.id 
    ORDER BY g.created_at DESC
", [$clientId, $clientId]);

// Get client's bookings
$bookings = $db->fetchAll("
    SELECT * FROM bookings 
    WHERE client_id = ? 
    ORDER BY session_date DESC 
    LIMIT 5
", [$clientId]);

// Get client's orders
$orders = $db->fetchAll("
    SELECT * FROM orders 
    WHERE client_id = ? 
    ORDER BY created_at DESC 
    LIMIT 5
", [$clientId]);

$title = "Client Portal - " . getSetting('site_title', SITE_NAME);
$description = "Access your private galleries and manage your orders.";
includeHeader($title, $description);
?>

<!-- Client Portal Header -->
<section class="section bg-gradient text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-3">Welcome, <?php echo htmlspecialchars($client['first_name']); ?>!</h1>
                <p class="lead mb-0">Access your private galleries, view your bookings, and manage your orders.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="client-logout.php" class="btn btn-outline-light">
                    <i class="fas fa-sign-out-alt me-1"></i>Logout
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Client Dashboard -->
<section class="section">
    <div class="container">
        <!-- Quick Stats -->
        <div class="row mb-5">
            <div class="col-md-3 mb-4">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-images fa-2x text-primary mb-3"></i>
                        <h4 class="card-title"><?php echo count($galleries); ?></h4>
                        <p class="card-text text-muted">Private Galleries</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-photo-video fa-2x text-success mb-3"></i>
                        <h4 class="card-title"><?php echo array_sum(array_column($galleries, 'photo_count')); ?></h4>
                        <p class="card-text text-muted">Total Photos</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-calendar-alt fa-2x text-info mb-3"></i>
                        <h4 class="card-title"><?php echo count($bookings); ?></h4>
                        <p class="card-text text-muted">Bookings</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-shopping-cart fa-2x text-warning mb-3"></i>
                        <h4 class="card-title"><?php echo count($orders); ?></h4>
                        <p class="card-text text-muted">Orders</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Private Galleries -->
            <div class="col-lg-8 mb-5">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3>Your Private Galleries</h3>
                </div>
                
                <?php if (!empty($galleries)) { ?>
                <div class="row">
                    <?php foreach ($galleries as $gallery) { ?>
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="position-relative">
                                <img src="<?php echo htmlspecialchars($gallery['cover_image'] ?: 'assets/images/placeholder-gallery.jpg'); ?>" 
                                     class="card-img-top" 
                                     alt="<?php echo htmlspecialchars($gallery['title']); ?>" 
                                     style="height: 200px; object-fit: cover;">
                                
                                <!-- Photo count badge -->
                                <div class="position-absolute top-0 end-0 m-3">
                                    <span class="badge bg-dark bg-opacity-75">
                                        <i class="fas fa-images me-1"></i><?php echo $gallery['photo_count']; ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="card-body">
                                <h5 class="card-title"><?php echo htmlspecialchars($gallery['title']); ?></h5>
                                <p class="card-text text-muted">
                                    <?php 
                                    $description = $gallery['description'];
                                    echo htmlspecialchars(strlen($description) > 100 ? substr($description, 0, 100) . '...' : $description); 
                                    ?>
                                </p>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        <?php echo date('M j, Y', strtotime($gallery['created_at'])); ?>
                                    </small>
                                    <?php if ($gallery['is_private']) { ?>
                                    <span class="badge bg-warning">Private</span>
                                    <?php } ?>
                                </div>
                            </div>
                            
                            <div class="card-footer bg-transparent">
                                <a href="client-gallery.php?slug=<?php echo htmlspecialchars($gallery['slug']); ?>" 
                                   class="btn btn-primary w-100">
                                    <i class="fas fa-eye me-2"></i>View Gallery
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php } ?>
                </div>
                <?php } else { ?>
                <div class="text-center py-5">
                    <i class="fas fa-images fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No galleries available yet</h5>
                    <p class="text-muted">Your private galleries will appear here once they're ready.</p>
                </div>
                <?php } ?>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Recent Bookings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Recent Bookings</h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($bookings)) { ?>
                        <?php foreach ($bookings as $booking) { ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h6 class="mb-1"><?php echo ucfirst($booking['session_type']); ?></h6>
                                <small class="text-muted">
                                    <?php echo date('M j, Y', strtotime($booking['session_date'])); ?>
                                </small>
                            </div>
                            <span class="badge bg-<?php 
                                echo $booking['status'] === 'confirmed' ? 'success' : 
                                    ($booking['status'] === 'pending' ? 'warning' : 'secondary'); 
                            ?>">
                                <?php echo ucfirst($booking['status']); ?>
                            </span>
                        </div>
                        <?php } ?>
                        <?php } else { ?>
                        <p class="text-muted mb-0">No bookings yet.</p>
                        <?php } ?>
                    </div>
                </div>
                
                <!-- Recent Orders -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Recent Orders</h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($orders)) { ?>
                        <?php foreach ($orders as $order) { ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h6 class="mb-1">#<?php echo htmlspecialchars($order['order_number']); ?></h6>
                                <small class="text-muted">
                                    $<?php echo number_format($order['total_amount'], 2); ?>
                                </small>
                            </div>
                            <span class="badge bg-<?php 
                                echo $order['payment_status'] === 'paid' ? 'success' : 
                                    ($order['payment_status'] === 'pending' ? 'warning' : 'danger'); 
                            ?>">
                                <?php echo ucfirst($order['payment_status']); ?>
                            </span>
                        </div>
                        <?php } ?>
                        <?php } else { ?>
                        <p class="text-muted mb-0">No orders yet.</p>
                        <?php } ?>
                    </div>
                </div>
                
                <!-- Account Info -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Account Information</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2"><strong>Name:</strong> <?php echo htmlspecialchars($client['first_name'] . ' ' . $client['last_name']); ?></p>
                        <p class="mb-2"><strong>Email:</strong> <?php echo htmlspecialchars($client['email']); ?></p>
                        <?php if ($client['phone']) { ?>
                        <p class="mb-2"><strong>Phone:</strong> <?php echo htmlspecialchars($client['phone']); ?></p>
                        <?php } ?>
                        <p class="mb-0"><strong>Member Since:</strong> <?php echo date('M Y', strtotime($client['created_at'])); ?></p>
                        
                        <hr>
                        
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#updateProfileModal">
                                <i class="fas fa-edit me-1"></i>Update Profile
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Update Profile Modal -->
<div class="modal fade" id="updateProfileModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Profile</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="update-profile.php">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="first_name" class="form-label">First Name</label>
                        <input type="text" class="form-control" id="first_name" name="first_name" 
                               value="<?php echo htmlspecialchars($client['first_name']); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="last_name" class="form-label">Last Name</label>
                        <input type="text" class="form-control" id="last_name" name="last_name" 
                               value="<?php echo htmlspecialchars($client['last_name']); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="phone" name="phone" 
                               value="<?php echo htmlspecialchars($client['phone'] ?? ''); ?>">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Profile</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php includeFooter(); ?>
