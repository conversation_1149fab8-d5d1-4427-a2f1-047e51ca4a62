<?php
require_once 'config/database.php';

try {
    $db = Database::getInstance();
    $admin = $db->fetchOne('SELECT email, first_name, last_name FROM users WHERE role = "admin" LIMIT 1');
    
    if ($admin) {
        echo "🔑 ADMIN LOGIN CREDENTIALS:\n";
        echo "=========================\n";
        echo "Email: " . $admin['email'] . "\n";
        echo "Name: " . $admin['first_name'] . ' ' . $admin['last_name'] . "\n";
        echo "Password: admin123 (default from setup)\n\n";
        echo "🌐 Login URL: http://localhost:8000/admin/login.php\n";
    } else {
        echo "❌ No admin user found\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
