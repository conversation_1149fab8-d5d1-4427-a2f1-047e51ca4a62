<?php
/**
 * Test Admin Pages with Simulated Login
 */

session_start();

// Simulate admin login session
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;
$_SESSION['admin_name'] = 'Admin User';
$_SESSION['admin_email'] = '<EMAIL>';

echo "🧪 TESTING ADMIN PAGES WITH SIMULATED LOGIN!\n";
echo "============================================\n\n";

echo "🔐 Simulated admin session:\n";
echo "   admin_logged_in: " . ($_SESSION['admin_logged_in'] ? 'true' : 'false') . "\n";
echo "   admin_id: {$_SESSION['admin_id']}\n";
echo "   admin_name: {$_SESSION['admin_name']}\n";
echo "   admin_email: {$_SESSION['admin_email']}\n\n";

// Test pages
$testPages = [
    'admin/index.php' => 'Admin Dashboard',
    'admin/clients.php' => 'Client Management',
    'admin/upload-photos.php' => 'Photo Upload System',
    'admin/bookings.php' => 'Booking Management',
    'admin/galleries.php' => 'Gallery Management'
];

foreach ($testPages as $page => $title) {
    echo "🧪 Testing: $title ($page)\n";
    
    try {
        // Start output buffering to capture any output
        ob_start();
        
        // Include the page
        include $page;
        
        // Get the output
        $output = ob_get_clean();
        
        // Check if there were any fatal errors
        if (strlen($output) > 0) {
            echo "   ✅ Page loaded successfully\n";
            echo "   📄 Output length: " . strlen($output) . " characters\n";
        } else {
            echo "   ⚠️  Page loaded but no output (might be redirecting)\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Error loading page: " . $e->getMessage() . "\n";
    } catch (Error $e) {
        echo "   ❌ Fatal error: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

echo "🎯 TEST RESULTS:\n";
echo "================\n";
echo "✅ Admin session simulation working\n";
echo "✅ Pages should now be accessible after login\n\n";

echo "🌐 NEXT STEPS:\n";
echo "=============\n";
echo "1. Log into admin panel with credentials above\n";
echo "2. Navigate to upload-photos.php\n";
echo "3. Navigate to bookings.php\n";
echo "4. Test all admin functionality\n\n";

echo "🚀 Admin Pages Test Complete!\n";
?>
