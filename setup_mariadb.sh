#!/bin/bash

echo "🚀 Setting up MariaDB for Kuderik Photo..."
echo "============================================"

# Check if MariaDB is running
if ! systemctl is-active --quiet mariadb; then
    echo "📋 Starting MariaDB service..."
    sudo systemctl start mariadb
    sudo systemctl enable mariadb
fi

echo "✅ MariaDB is running!"

# Run the database setup
echo "📊 Setting up database and user..."
sudo mysql -u root < setup_database.sql

if [ $? -eq 0 ]; then
    echo "✅ Database setup completed successfully!"
    echo ""
    echo "📋 Database Configuration:"
    echo "  Database: kuderik_photo"
    echo "  Username: kuderik_user"
    echo "  Password: kuderik_password_2024"
    echo "  Host: localhost"
    echo ""
    echo "🌐 You can now access the website at:"
    echo "  Main site: http://localhost:8000"
    echo "  Admin panel: http://localhost:8000/admin/"
    echo "  Default admin login: admin / admin123"
    echo ""
    echo "🔧 Next steps:"
    echo "  1. Visit http://localhost:8000 to see the website"
    echo "  2. Go to http://localhost:8000/admin/ to access admin panel"
    echo "  3. Change the default admin password!"
else
    echo "❌ Database setup failed. Please check the error messages above."
    echo ""
    echo "🔧 Manual setup option:"
    echo "  1. Run: sudo mysql -u root"
    echo "  2. Execute the SQL commands from setup_database.sql manually"
fi
