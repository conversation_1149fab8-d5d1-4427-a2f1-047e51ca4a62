<?php
/**
 * Add sample media items to galleries for testing
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

try {
    $db = Database::getInstance();
    
    echo "Adding sample media items to galleries...\n";
    
    // Get all galleries
    $galleries = $db->fetchAll("SELECT id, title, slug FROM galleries");
    
    // Sample image URLs from placeholder services
    $sampleImages = [
        'https://picsum.photos/800/600?random=1',
        'https://picsum.photos/800/600?random=2', 
        'https://picsum.photos/800/600?random=3',
        'https://picsum.photos/800/600?random=4',
        'https://picsum.photos/800/600?random=5',
        'https://picsum.photos/800/600?random=6',
        'https://picsum.photos/800/600?random=7',
        'https://picsum.photos/800/600?random=8'
    ];
    
    $added = 0;
    
    foreach ($galleries as $gallery) {
        echo "Adding media to: " . $gallery['title'] . "\n";
        
        // Add 3-4 sample images per gallery
        $imageCount = rand(3, 4);
        
        for ($i = 0; $i < $imageCount; $i++) {
            $imageUrl = $sampleImages[array_rand($sampleImages)];
            $filename = 'sample_' . $gallery['id'] . '_' . ($i + 1) . '.jpg';
            
            // Check if this media item already exists
            $existing = $db->fetchOne(
                "SELECT id FROM media_items WHERE gallery_id = ? AND filename = ?",
                [$gallery['id'], $filename]
            );
            
            if (!$existing) {
                $db->query(
                    "INSERT INTO media_items (gallery_id, title, filename, original_filename, file_path, thumbnail_path, file_type, file_size, mime_type, width, height, sort_order, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    [
                        $gallery['id'],
                        ucfirst($gallery['slug']) . ' Sample ' . ($i + 1),
                        $filename,
                        $filename,
                        $imageUrl, // Using external URL for demo
                        $imageUrl, // Same for thumbnail
                        'image',
                        150000, // 150KB
                        'image/jpeg',
                        800,
                        600,
                        $i,
                        1
                    ]
                );
                $added++;
            }
        }
    }
    
    echo "\n🎉 Sample media setup complete!\n";
    echo "Added: $added media items\n";
    echo "\nYou can now view galleries with sample images at:\n";
    foreach ($galleries as $gallery) {
        echo "- http://localhost:8000/gallery/" . $gallery['slug'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
