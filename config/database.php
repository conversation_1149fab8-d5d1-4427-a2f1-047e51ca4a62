<?php
/**
 * Database Configuration
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'kuderik_photo');
define('DB_USER', 'kuderik_user');
define('DB_PASS', 'kuderik_password_2024');

// Site configuration
define('SITE_NAME', 'Kuderik Photography');
define('SITE_DESCRIPTION', 'Professional Photography Services by Kuderik');
define('SITE_URL', 'https://kuderik.com');
define('ADMIN_EMAIL', '<EMAIL>');

// Upload configuration
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_VIDEO_TYPES', ['mp4', 'webm', 'mov']);

// Thumbnail configuration
define('THUMB_WIDTH', 400);
define('THUMB_HEIGHT', 300);
define('THUMB_QUALITY', 85);

class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            // Try MariaDB/MySQL first
            $this->connection = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
        } catch (PDOException $e) {
            // Fallback to SQLite for testing
            try {
                $this->connection = new PDO(
                    "sqlite:" . __DIR__ . "/kuderik_photo.db",
                    null,
                    null,
                    [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                    ]
                );
                $this->initializeSQLite();
            } catch (PDOException $e2) {
                die("Database connection failed: " . $e->getMessage() . " (SQLite fallback also failed: " . $e2->getMessage() . ")");
            }
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        $stmt = $this->connection->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    }
    
    public function fetchAll($sql, $params = []) {
        return $this->query($sql, $params)->fetchAll();
    }
    
    public function fetchOne($sql, $params = []) {
        return $this->query($sql, $params)->fetch();
    }
    
    public function lastInsertId() {
        return $this->connection->lastInsertId();
    }

    /**
     * Initialize SQLite database with required tables
     */
    private function initializeSQLite() {
        $sql = "
        -- Admin users table
        CREATE TABLE IF NOT EXISTS admin_users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(50),
            last_name VARCHAR(50),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_login DATETIME NULL,
            is_active BOOLEAN DEFAULT 1
        );

        -- Galleries table
        CREATE TABLE IF NOT EXISTS galleries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(200) NOT NULL,
            slug VARCHAR(200) NOT NULL UNIQUE,
            description TEXT,
            cover_image VARCHAR(255),
            sort_order INTEGER DEFAULT 0,
            is_featured BOOLEAN DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- Media items table
        CREATE TABLE IF NOT EXISTS media_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            gallery_id INTEGER,
            title VARCHAR(200),
            description TEXT,
            filename VARCHAR(255) NOT NULL,
            original_filename VARCHAR(255),
            file_path VARCHAR(500) NOT NULL,
            thumbnail_path VARCHAR(500),
            file_type VARCHAR(10) NOT NULL,
            file_size INTEGER,
            mime_type VARCHAR(100),
            width INTEGER,
            height INTEGER,
            duration INTEGER,
            sort_order INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (gallery_id) REFERENCES galleries(id) ON DELETE CASCADE
        );

        -- Site settings table
        CREATE TABLE IF NOT EXISTS site_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT,
            setting_type VARCHAR(20) DEFAULT 'text',
            description TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- Contact messages table
        CREATE TABLE IF NOT EXISTS contact_messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            subject VARCHAR(200),
            message TEXT NOT NULL,
            phone VARCHAR(20),
            is_read BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        ";

        $this->connection->exec($sql);

        // Insert default data if tables are empty
        $this->insertDefaultData();
    }

    /**
     * Insert default data for SQLite
     */
    private function insertDefaultData() {
        // Check if admin user exists
        $adminExists = $this->fetchOne("SELECT COUNT(*) as count FROM admin_users");
        if ($adminExists['count'] == 0) {
            // Insert default admin user (password: admin123)
            $this->query(
                "INSERT INTO admin_users (username, email, password, first_name, last_name) VALUES (?, ?, ?, ?, ?)",
                ['admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User']
            );
        }

        // Check if settings exist
        $settingsExist = $this->fetchOne("SELECT COUNT(*) as count FROM site_settings");
        if ($settingsExist['count'] == 0) {
            $defaultSettings = [
                ['site_title', 'Kuderik Photo', 'text', 'Main site title'],
                ['site_tagline', 'Multimedia genius specializing in photography and video productions', 'text', 'Site tagline/description'],
                ['hero_title', 'Capturing Life\'s Moments', 'text', 'Hero section title'],
                ['hero_subtitle', 'Professional Photography & Video Production', 'text', 'Hero section subtitle'],
                ['about_text', 'Welcome to Kuderik Photo, where creativity meets technical excellence.', 'textarea', 'About section text'],
                ['contact_email', '<EMAIL>', 'text', 'Contact email address'],
                ['contact_phone', '+****************', 'text', 'Contact phone number'],
                ['footer_text', '© 2024 Kuderik Photo. All rights reserved.', 'text', 'Footer copyright text']
            ];

            foreach ($defaultSettings as $setting) {
                $this->query(
                    "INSERT INTO site_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)",
                    $setting
                );
            }
        }

        // Check if sample galleries exist
        $galleriesExist = $this->fetchOne("SELECT COUNT(*) as count FROM galleries");
        if ($galleriesExist['count'] == 0) {
            $sampleGalleries = [
                ['Portrait Photography', 'portrait-photography', 'Professional portrait sessions capturing personality and emotion', 1],
                ['Wedding Photography', 'wedding-photography', 'Beautiful wedding moments preserved for a lifetime', 1],
                ['Food Photography', 'food-photography', 'Mouth-watering food photography for restaurants and culinary brands', 1],
                ['Product Photography', 'product-photography', 'High-quality product shots for e-commerce and marketing', 1],
                ['360 Tours', '360-tours', 'Immersive 360-degree virtual tours for businesses and venues', 1],
                ['Commercial Photography', 'commercial-photography', 'Professional commercial and corporate photography services', 0],
                ['Video Productions', 'video-productions', 'Creative video content for all occasions', 0]
            ];

            foreach ($sampleGalleries as $gallery) {
                $this->query(
                    "INSERT INTO galleries (title, slug, description, is_featured) VALUES (?, ?, ?, ?)",
                    $gallery
                );
            }
        }
    }
}
?>
