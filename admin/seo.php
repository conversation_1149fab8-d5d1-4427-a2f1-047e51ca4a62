<?php
/**
 * Admin - SEO Analysis and Tools
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAuth();

$db = Database::getInstance();
$galleryModel = new Gallery();
$mediaModel = new MediaItem();

// SEO Analysis
$seoIssues = [];
$seoScore = 100;

// Check basic settings
$siteTitle = getSetting('site_title');
$siteDescription = getSetting('site_tagline');
$metaKeywords = getSetting('meta_keywords');

if (empty($siteTitle)) {
    $seoIssues[] = 'Site title is not set';
    $seoScore -= 10;
}

if (empty($siteDescription)) {
    $seoIssues[] = 'Site description is not set';
    $seoScore -= 10;
}

if (empty($metaKeywords)) {
    $seoIssues[] = 'Meta keywords are not set';
    $seoScore -= 5;
}

// Check galleries
$galleries = $galleryModel->getAllActive();
$galleriesWithoutDescription = 0;
$galleriesWithShortDescription = 0;

foreach ($galleries as $gallery) {
    if (empty($gallery['description'])) {
        $galleriesWithoutDescription++;
    } elseif (strlen($gallery['description']) < 50) {
        $galleriesWithShortDescription++;
    }
}

if ($galleriesWithoutDescription > 0) {
    $seoIssues[] = "$galleriesWithoutDescription galleries missing descriptions";
    $seoScore -= min($galleriesWithoutDescription * 2, 10);
}

if ($galleriesWithShortDescription > 0) {
    $seoIssues[] = "$galleriesWithShortDescription galleries have short descriptions (< 50 chars)";
    $seoScore -= min($galleriesWithShortDescription, 5);
}

// Check media items
$mediaItems = $mediaModel->getAll();
$mediaWithoutTitles = 0;
$mediaWithoutAlt = 0;

foreach ($mediaItems as $item) {
    if (empty($item['title'])) {
        $mediaWithoutTitles++;
    }
}

if ($mediaWithoutTitles > 0) {
    $seoIssues[] = "$mediaWithoutTitles media items missing titles";
    $seoScore -= min($mediaWithoutTitles, 10);
}

// Check social media
$socialPlatforms = ['facebook', 'instagram', 'twitter', 'youtube'];
$missingSocial = 0;

foreach ($socialPlatforms as $platform) {
    if (empty(getSetting('social_' . $platform))) {
        $missingSocial++;
    }
}

if ($missingSocial > 2) {
    $seoIssues[] = 'Limited social media presence';
    $seoScore -= 5;
}

// Check Google Analytics
if (empty(getSetting('google_analytics'))) {
    $seoIssues[] = 'Google Analytics not configured';
    $seoScore -= 5;
}

$seoScore = max($seoScore, 0);

$pageTitle = "SEO Analysis";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">SEO Analysis</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="../sitemap.xml.php" target="_blank" class="btn btn-outline-primary me-2">
                        <i class="fas fa-sitemap me-1"></i>View Sitemap
                    </a>
                    <a href="../robots.txt" target="_blank" class="btn btn-outline-secondary">
                        <i class="fas fa-robot me-1"></i>View Robots.txt
                    </a>
                </div>
            </div>

            <!-- SEO Score -->
            <div class="row mb-4">
                <div class="col-lg-4">
                    <div class="card border-left-<?php echo $seoScore >= 80 ? 'success' : ($seoScore >= 60 ? 'warning' : 'danger'); ?> shadow">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-<?php echo $seoScore >= 80 ? 'success' : ($seoScore >= 60 ? 'warning' : 'danger'); ?> text-uppercase mb-1">SEO Score</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $seoScore; ?>/100</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-search fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card border-left-info shadow">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Pages</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo 5 + count($galleries); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card border-left-primary shadow">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Issues Found</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo count($seoIssues); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- SEO Issues -->
            <?php if (!empty($seoIssues)) { ?>
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>SEO Issues to Fix
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <?php foreach ($seoIssues as $issue) { ?>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <?php echo htmlspecialchars($issue); ?>
                            <span class="badge bg-warning">Action Needed</span>
                        </li>
                        <?php } ?>
                    </ul>
                </div>
            </div>
            <?php } else { ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                Great! No SEO issues found. Your site is well optimized.
            </div>
            <?php } ?>

            <!-- SEO Tools -->
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-tools me-2"></i>SEO Tools
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <a href="../sitemap.xml.php" target="_blank" class="list-group-item list-group-item-action">
                                    <i class="fas fa-sitemap me-2"></i>XML Sitemap
                                    <small class="text-muted d-block">Automatically generated sitemap for search engines</small>
                                </a>
                                <a href="../robots.txt" target="_blank" class="list-group-item list-group-item-action">
                                    <i class="fas fa-robot me-2"></i>Robots.txt
                                    <small class="text-muted d-block">Search engine crawling instructions</small>
                                </a>
                                <a href="settings.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-cog me-2"></i>SEO Settings
                                    <small class="text-muted d-block">Configure meta tags and analytics</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-chart-line me-2"></i>SEO Statistics
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6 mb-3">
                                    <div class="border-right">
                                        <h4 class="text-primary"><?php echo count($galleries); ?></h4>
                                        <small class="text-muted">Galleries</small>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <h4 class="text-success"><?php echo count($mediaItems); ?></h4>
                                    <small class="text-muted">Media Items</small>
                                </div>
                                <div class="col-6">
                                    <div class="border-right">
                                        <h4 class="text-info"><?php echo count($galleries) - $galleriesWithoutDescription; ?></h4>
                                        <small class="text-muted">Optimized Galleries</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-warning"><?php echo count($mediaItems) - $mediaWithoutTitles; ?></h4>
                                    <small class="text-muted">Titled Media</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- SEO Recommendations -->
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-lightbulb me-2"></i>SEO Recommendations
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Content Optimization</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Add descriptive titles to all media items</li>
                                <li><i class="fas fa-check text-success me-2"></i>Write detailed gallery descriptions (100+ words)</li>
                                <li><i class="fas fa-check text-success me-2"></i>Use relevant keywords in content</li>
                                <li><i class="fas fa-check text-success me-2"></i>Optimize image file names</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Technical SEO</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Submit sitemap to Google Search Console</li>
                                <li><i class="fas fa-check text-success me-2"></i>Set up Google Analytics tracking</li>
                                <li><i class="fas fa-check text-success me-2"></i>Optimize page loading speed</li>
                                <li><i class="fas fa-check text-success me-2"></i>Ensure mobile responsiveness</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
