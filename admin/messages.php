<?php
/**
 * Admin - Contact Messages Management
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAuth();

$db = Database::getInstance();
$message = '';
$messageType = '';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'mark_read') {
        $id = (int)$_POST['id'];
        try {
            $db->query("UPDATE contact_messages SET is_read = 1 WHERE id = ?", [$id]);
            $message = 'Message marked as read.';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'Error updating message: ' . $e->getMessage();
            $messageType = 'error';
        }
    } elseif ($action === 'mark_unread') {
        $id = (int)$_POST['id'];
        try {
            $db->query("UPDATE contact_messages SET is_read = 0 WHERE id = ?", [$id]);
            $message = 'Message marked as unread.';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'Error updating message: ' . $e->getMessage();
            $messageType = 'error';
        }
    } elseif ($action === 'delete') {
        $id = (int)$_POST['id'];
        try {
            $db->query("DELETE FROM contact_messages WHERE id = ?", [$id]);
            $message = 'Message deleted successfully.';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'Error deleting message: ' . $e->getMessage();
            $messageType = 'error';
        }
    } elseif ($action === 'bulk_action') {
        $bulkAction = $_POST['bulk_action'] ?? '';
        $selectedIds = $_POST['selected_messages'] ?? [];
        
        if (!empty($selectedIds) && !empty($bulkAction)) {
            $placeholders = str_repeat('?,', count($selectedIds) - 1) . '?';
            
            try {
                if ($bulkAction === 'mark_read') {
                    $db->query("UPDATE contact_messages SET is_read = 1 WHERE id IN ($placeholders)", $selectedIds);
                    $message = count($selectedIds) . ' messages marked as read.';
                } elseif ($bulkAction === 'mark_unread') {
                    $db->query("UPDATE contact_messages SET is_read = 0 WHERE id IN ($placeholders)", $selectedIds);
                    $message = count($selectedIds) . ' messages marked as unread.';
                } elseif ($bulkAction === 'delete') {
                    $db->query("DELETE FROM contact_messages WHERE id IN ($placeholders)", $selectedIds);
                    $message = count($selectedIds) . ' messages deleted.';
                }
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'Error performing bulk action: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
    }
}

// Get filter parameters
$statusFilter = $_GET['status'] ?? 'all';
$searchQuery = $_GET['search'] ?? '';

// Build query
$sql = "SELECT * FROM contact_messages WHERE 1=1";
$params = [];

if ($statusFilter === 'unread') {
    $sql .= " AND is_read = 0";
} elseif ($statusFilter === 'read') {
    $sql .= " AND is_read = 1";
}

if (!empty($searchQuery)) {
    $sql .= " AND (name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)";
    $searchParam = '%' . $searchQuery . '%';
    $params = array_merge($params, [$searchParam, $searchParam, $searchParam, $searchParam]);
}

$sql .= " ORDER BY created_at DESC";

$messages = $db->fetchAll($sql, $params);

// Get statistics
$totalMessages = $db->fetchOne("SELECT COUNT(*) as count FROM contact_messages")['count'];
$unreadMessages = $db->fetchOne("SELECT COUNT(*) as count FROM contact_messages WHERE is_read = 0")['count'];
$readMessages = $totalMessages - $unreadMessages;

$pageTitle = "Messages";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Contact Messages</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <span class="badge bg-primary me-2">Total: <?php echo $totalMessages; ?></span>
                        <span class="badge bg-warning me-2">Unread: <?php echo $unreadMessages; ?></span>
                        <span class="badge bg-success">Read: <?php echo $readMessages; ?></span>
                    </div>
                </div>
            </div>

            <!-- Alert Container -->
            <div class="alert-container">
                <?php if (!empty($message)) { ?>
                <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php } ?>
            </div>

            <!-- Filters -->
            <div class="card shadow mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" name="status" onchange="this.form.submit()">
                                <option value="all" <?php echo $statusFilter === 'all' ? 'selected' : ''; ?>>All Messages</option>
                                <option value="unread" <?php echo $statusFilter === 'unread' ? 'selected' : ''; ?>>Unread Only</option>
                                <option value="read" <?php echo $statusFilter === 'read' ? 'selected' : ''; ?>>Read Only</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($searchQuery); ?>" 
                                   placeholder="Search by name, email, subject, or message...">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">Search</button>
                            <a href="messages.php" class="btn btn-outline-secondary">Clear</a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Messages -->
            <div class="card shadow">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-0 font-weight-bold text-primary">Contact Messages</h6>
                        </div>
                        <div class="col-auto">
                            <form method="POST" id="bulkForm" style="display: inline;">
                                <input type="hidden" name="action" value="bulk_action">
                                <div class="input-group input-group-sm">
                                    <select class="form-select" name="bulk_action" required>
                                        <option value="">Bulk Actions</option>
                                        <option value="mark_read">Mark as Read</option>
                                        <option value="mark_unread">Mark as Unread</option>
                                        <option value="delete">Delete</option>
                                    </select>
                                    <button type="submit" class="btn btn-outline-primary">Apply</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (!empty($messages)) { ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="30">
                                        <input type="checkbox" id="selectAll">
                                    </th>
                                    <th>From</th>
                                    <th>Subject</th>
                                    <th>Message</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($messages as $msg) { ?>
                                <tr class="<?php echo !$msg['is_read'] ? 'table-warning' : ''; ?>">
                                    <td>
                                        <input type="checkbox" name="selected_messages[]" value="<?php echo $msg['id']; ?>" form="bulkForm">
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($msg['name']); ?></strong><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($msg['email']); ?></small>
                                        <?php if (!empty($msg['phone'])) { ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($msg['phone']); ?></small>
                                        <?php } ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($msg['subject'])) { ?>
                                        <strong><?php echo htmlspecialchars($msg['subject']); ?></strong>
                                        <?php } else { ?>
                                        <em class="text-muted">No subject</em>
                                        <?php } ?>
                                    </td>
                                    <td>
                                        <div class="message-preview">
                                            <?php echo htmlspecialchars(substr($msg['message'], 0, 100)) . (strlen($msg['message']) > 100 ? '...' : ''); ?>
                                        </div>
                                        <button type="button" class="btn btn-link btn-sm p-0 view-full-message" 
                                                data-message="<?php echo htmlspecialchars($msg['message']); ?>"
                                                data-name="<?php echo htmlspecialchars($msg['name']); ?>">
                                            View Full Message
                                        </button>
                                    </td>
                                    <td>
                                        <small><?php echo date('M j, Y g:i A', strtotime($msg['created_at'])); ?></small>
                                    </td>
                                    <td>
                                        <?php if ($msg['is_read']) { ?>
                                        <span class="badge bg-success">Read</span>
                                        <?php } else { ?>
                                        <span class="badge bg-warning">Unread</span>
                                        <?php } ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <?php if (!$msg['is_read']) { ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="mark_read">
                                                <input type="hidden" name="id" value="<?php echo $msg['id']; ?>">
                                                <button type="submit" class="btn btn-outline-success" title="Mark as Read">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                            <?php } else { ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="mark_unread">
                                                <input type="hidden" name="id" value="<?php echo $msg['id']; ?>">
                                                <button type="submit" class="btn btn-outline-warning" title="Mark as Unread">
                                                    <i class="fas fa-undo"></i>
                                                </button>
                                            </form>
                                            <?php } ?>
                                            
                                            <a href="mailto:<?php echo htmlspecialchars($msg['email']); ?>?subject=Re: <?php echo htmlspecialchars($msg['subject']); ?>" 
                                               class="btn btn-outline-primary" title="Reply">
                                                <i class="fas fa-reply"></i>
                                            </a>
                                            
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="id" value="<?php echo $msg['id']; ?>">
                                                <button type="submit" class="btn btn-outline-danger btn-delete" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php } ?>
                            </tbody>
                        </table>
                    </div>
                    <?php } else { ?>
                    <div class="text-center py-4">
                        <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Messages Found</h5>
                        <p class="text-muted">
                            <?php if (!empty($searchQuery) || $statusFilter !== 'all') { ?>
                            No messages match your current filters.
                            <?php } else { ?>
                            No contact messages have been received yet.
                            <?php } ?>
                        </p>
                    </div>
                    <?php } ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Full Message Modal -->
<div class="modal fade" id="messageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Message from <span id="messageSender"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="fullMessage" style="white-space: pre-wrap;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Select all checkbox
    $('#selectAll').change(function() {
        $('input[name="selected_messages[]"]').prop('checked', this.checked);
    });
    
    // View full message
    $('.view-full-message').click(function() {
        const message = $(this).data('message');
        const name = $(this).data('name');
        
        $('#messageSender').text(name);
        $('#fullMessage').text(message);
        $('#messageModal').modal('show');
    });
    
    // Bulk form validation
    $('#bulkForm').submit(function(e) {
        const selectedMessages = $('input[name="selected_messages[]"]:checked').length;
        const bulkAction = $('select[name="bulk_action"]').val();
        
        if (selectedMessages === 0) {
            e.preventDefault();
            alert('Please select at least one message.');
            return false;
        }
        
        if (bulkAction === 'delete') {
            if (!confirm('Are you sure you want to delete the selected messages? This action cannot be undone.')) {
                e.preventDefault();
                return false;
            }
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
