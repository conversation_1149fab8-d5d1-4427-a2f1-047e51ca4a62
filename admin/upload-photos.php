<?php
/**
 * Modern Photo Upload System with Drag & Drop
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance();
$message = '';
$messageType = '';

// Handle AJAX upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['photos'])) {
    header('Content-Type: application/json');
    
    $galleryId = $_POST['gallery_id'] ?? null;
    if (!$galleryId) {
        echo json_encode(['success' => false, 'message' => 'Gallery ID is required']);
        exit;
    }
    
    $uploadDir = '../uploads/galleries/' . $galleryId . '/';
    $thumbDir = '../uploads/galleries/' . $galleryId . '/thumbs/';
    
    // Create directories if they don't exist
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    if (!file_exists($thumbDir)) {
        mkdir($thumbDir, 0755, true);
    }
    
    $uploadedFiles = [];
    $errors = [];
    
    foreach ($_FILES['photos']['tmp_name'] as $key => $tmpName) {
        if ($_FILES['photos']['error'][$key] === UPLOAD_ERR_OK) {
            $originalName = $_FILES['photos']['name'][$key];
            $fileSize = $_FILES['photos']['size'][$key];
            $mimeType = $_FILES['photos']['type'][$key];
            
            // Validate file type
            $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($mimeType, $allowedTypes)) {
                $errors[] = "Invalid file type for $originalName";
                continue;
            }
            
            // Generate unique filename
            $extension = pathinfo($originalName, PATHINFO_EXTENSION);
            $filename = uniqid() . '_' . time() . '.' . $extension;
            $filePath = $uploadDir . $filename;
            $thumbPath = $thumbDir . $filename;
            
            if (move_uploaded_file($tmpName, $filePath)) {
                // Get image dimensions
                $imageInfo = getimagesize($filePath);
                $width = $imageInfo[0] ?? 0;
                $height = $imageInfo[1] ?? 0;
                
                // Create thumbnail
                createThumbnail($filePath, $thumbPath, 400, 300);
                
                // Save to database
                try {
                    $db->query("
                        INSERT INTO media_items (gallery_id, filename, original_filename, file_path, thumbnail_path, 
                                                file_type, file_size, mime_type, width, height, is_active) 
                        VALUES (?, ?, ?, ?, ?, 'image', ?, ?, ?, ?, 1)
                    ", [
                        $galleryId,
                        $filename,
                        $originalName,
                        'uploads/galleries/' . $galleryId . '/' . $filename,
                        'uploads/galleries/' . $galleryId . '/thumbs/' . $filename,
                        $fileSize,
                        $mimeType,
                        $width,
                        $height
                    ]);
                    
                    $uploadedFiles[] = [
                        'id' => $db->getConnection()->lastInsertId(),
                        'filename' => $filename,
                        'original_name' => $originalName,
                        'thumbnail' => 'uploads/galleries/' . $galleryId . '/thumbs/' . $filename
                    ];
                } catch (Exception $e) {
                    $errors[] = "Database error for $originalName: " . $e->getMessage();
                    unlink($filePath);
                    if (file_exists($thumbPath)) unlink($thumbPath);
                }
            } else {
                $errors[] = "Failed to upload $originalName";
            }
        } else {
            $errors[] = "Upload error for " . $_FILES['photos']['name'][$key];
        }
    }
    
    echo json_encode([
        'success' => empty($errors),
        'uploaded' => $uploadedFiles,
        'errors' => $errors,
        'total_uploaded' => count($uploadedFiles)
    ]);
    exit;
}

// Get galleries for dropdown
$galleries = $db->fetchAll("SELECT id, title FROM galleries ORDER BY title ASC");

$pageTitle = "Upload Photos";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Upload Photos</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="galleries.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Galleries
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <!-- Upload Form -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Upload Photos</h6>
                        </div>
                        <div class="card-body">
                            <form id="uploadForm" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <label for="gallery_id" class="form-label">Select Gallery *</label>
                                    <select class="form-select" id="gallery_id" name="gallery_id" required>
                                        <option value="">Choose a gallery...</option>
                                        <?php foreach ($galleries as $gallery) { ?>
                                        <option value="<?php echo $gallery['id']; ?>"><?php echo htmlspecialchars($gallery['title']); ?></option>
                                        <?php } ?>
                                    </select>
                                </div>
                                
                                <!-- Drag & Drop Upload Area -->
                                <div class="upload-area" id="uploadArea">
                                    <div class="upload-content">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <h5>Drag & Drop Photos Here</h5>
                                        <p class="text-muted">or click to browse files</p>
                                        <input type="file" id="photoInput" name="photos[]" multiple accept="image/*" style="display: none;">
                                        <button type="button" class="btn btn-primary" onclick="document.getElementById('photoInput').click()">
                                            <i class="fas fa-plus me-1"></i>Select Photos
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- Upload Progress -->
                                <div id="uploadProgress" style="display: none;">
                                    <div class="progress mb-3">
                                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <div id="uploadStatus"></div>
                                </div>
                                
                                <!-- Selected Files Preview -->
                                <div id="filePreview" class="mt-3" style="display: none;">
                                    <h6>Selected Files:</h6>
                                    <div id="previewContainer" class="row"></div>
                                    <button type="submit" class="btn btn-success mt-3" id="uploadBtn" disabled>
                                        <i class="fas fa-upload me-1"></i>Upload Photos
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <!-- Upload Guidelines -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-info">Upload Guidelines</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Supported formats: JPG, PNG, GIF, WebP</li>
                                <li><i class="fas fa-check text-success me-2"></i>Maximum file size: 10MB per image</li>
                                <li><i class="fas fa-check text-success me-2"></i>Recommended resolution: 2048x2048px or higher</li>
                                <li><i class="fas fa-check text-success me-2"></i>Images will be automatically resized if needed</li>
                                <li><i class="fas fa-check text-success me-2"></i>Thumbnails are generated automatically</li>
                            </ul>
                            
                            <hr>
                            
                            <h6 class="font-weight-bold">Tips:</h6>
                            <ul class="list-unstyled small text-muted">
                                <li>• Use descriptive filenames</li>
                                <li>• Upload in batches for better performance</li>
                                <li>• Ensure good image quality before uploading</li>
                                <li>• Consider watermarking sensitive images</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<style>
.upload-area {
    border: 2px dashed #ddd;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background: #f8f9fa;
}

.upload-area:hover, .upload-area.dragover {
    border-color: #007bff;
    background: #e3f2fd;
}

.upload-area.dragover {
    border-color: #28a745;
    background: #d4edda;
}

.preview-item {
    position: relative;
    margin-bottom: 15px;
}

.preview-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 5px;
}

.preview-item .remove-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(220, 53, 69, 0.8);
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    color: white;
    font-size: 12px;
}
</style>

<script>
let selectedFiles = [];

// Drag and drop functionality
const uploadArea = document.getElementById('uploadArea');
const photoInput = document.getElementById('photoInput');
const filePreview = document.getElementById('filePreview');
const previewContainer = document.getElementById('previewContainer');
const uploadBtn = document.getElementById('uploadBtn');

uploadArea.addEventListener('click', () => photoInput.click());

uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    handleFiles(e.dataTransfer.files);
});

photoInput.addEventListener('change', (e) => {
    handleFiles(e.target.files);
});

function handleFiles(files) {
    selectedFiles = Array.from(files);
    displayPreview();
}

function displayPreview() {
    previewContainer.innerHTML = '';
    
    if (selectedFiles.length === 0) {
        filePreview.style.display = 'none';
        uploadBtn.disabled = true;
        return;
    }
    
    filePreview.style.display = 'block';
    uploadBtn.disabled = false;
    
    selectedFiles.forEach((file, index) => {
        const reader = new FileReader();
        reader.onload = (e) => {
            const col = document.createElement('div');
            col.className = 'col-md-3 col-sm-4 col-6';
            col.innerHTML = `
                <div class="preview-item">
                    <img src="${e.target.result}" alt="${file.name}">
                    <button type="button" class="btn remove-btn" onclick="removeFile(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                    <small class="d-block text-muted mt-1">${file.name}</small>
                </div>
            `;
            previewContainer.appendChild(col);
        };
        reader.readAsDataURL(file);
    });
}

function removeFile(index) {
    selectedFiles.splice(index, 1);
    displayPreview();
}

// Upload form submission
document.getElementById('uploadForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const galleryId = document.getElementById('gallery_id').value;
    if (!galleryId) {
        alert('Please select a gallery first');
        return;
    }
    
    if (selectedFiles.length === 0) {
        alert('Please select files to upload');
        return;
    }
    
    const formData = new FormData();
    formData.append('gallery_id', galleryId);
    
    selectedFiles.forEach((file, index) => {
        formData.append(`photos[${index}]`, file);
    });
    
    // Show progress
    const progressDiv = document.getElementById('uploadProgress');
    const progressBar = progressDiv.querySelector('.progress-bar');
    const statusDiv = document.getElementById('uploadStatus');
    
    progressDiv.style.display = 'block';
    uploadBtn.disabled = true;
    
    try {
        const response = await fetch('upload-photos.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            progressBar.style.width = '100%';
            progressBar.className = 'progress-bar bg-success';
            statusDiv.innerHTML = `<div class="alert alert-success">Successfully uploaded ${result.total_uploaded} photos!</div>`;
            
            // Reset form
            selectedFiles = [];
            displayPreview();
            document.getElementById('uploadForm').reset();
            
            setTimeout(() => {
                progressDiv.style.display = 'none';
                progressBar.style.width = '0%';
                progressBar.className = 'progress-bar';
            }, 3000);
        } else {
            progressBar.className = 'progress-bar bg-danger';
            statusDiv.innerHTML = `<div class="alert alert-danger">Upload failed: ${result.errors.join(', ')}</div>`;
        }
    } catch (error) {
        progressBar.className = 'progress-bar bg-danger';
        statusDiv.innerHTML = `<div class="alert alert-danger">Upload error: ${error.message}</div>`;
    }
    
    uploadBtn.disabled = false;
});
</script>

<?php
// Thumbnail creation function
function createThumbnail($source, $destination, $maxWidth, $maxHeight) {
    $imageInfo = getimagesize($source);
    if (!$imageInfo) return false;
    
    $width = $imageInfo[0];
    $height = $imageInfo[1];
    $type = $imageInfo[2];
    
    // Calculate new dimensions
    $ratio = min($maxWidth / $width, $maxHeight / $height);
    $newWidth = round($width * $ratio);
    $newHeight = round($height * $ratio);
    
    // Create source image
    switch ($type) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($source);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($source);
            break;
        case IMAGETYPE_GIF:
            $sourceImage = imagecreatefromgif($source);
            break;
        default:
            return false;
    }
    
    // Create thumbnail
    $thumbnail = imagecreatetruecolor($newWidth, $newHeight);
    
    // Preserve transparency for PNG and GIF
    if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
        imagealphablending($thumbnail, false);
        imagesavealpha($thumbnail, true);
        $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
        imagefilledrectangle($thumbnail, 0, 0, $newWidth, $newHeight, $transparent);
    }
    
    imagecopyresampled($thumbnail, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
    
    // Save thumbnail
    switch ($type) {
        case IMAGETYPE_JPEG:
            imagejpeg($thumbnail, $destination, 85);
            break;
        case IMAGETYPE_PNG:
            imagepng($thumbnail, $destination);
            break;
        case IMAGETYPE_GIF:
            imagegif($thumbnail, $destination);
            break;
    }
    
    imagedestroy($sourceImage);
    imagedestroy($thumbnail);
    
    return true;
}

include 'includes/footer.php';
?>
