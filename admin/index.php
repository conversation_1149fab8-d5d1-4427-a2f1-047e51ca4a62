<?php
/**
 * Admin Panel - Main Dashboard
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// Get dashboard statistics
$db = Database::getInstance();

// Count galleries
$galleryCount = $db->fetchOne("SELECT COUNT(*) as count FROM galleries")['count'];
$activeGalleryCount = $db->fetchOne("SELECT COUNT(*) as count FROM galleries WHERE is_active = 1")['count'];

// Count media items
$mediaCount = $db->fetchOne("SELECT COUNT(*) as count FROM media_items")['count'];
$activeMediaCount = $db->fetchOne("SELECT COUNT(*) as count FROM media_items WHERE is_active = 1")['count'];

// Count contact messages
$messageCount = $db->fetchOne("SELECT COUNT(*) as count FROM contact_messages")['count'];
$unreadMessageCount = $db->fetchOne("SELECT COUNT(*) as count FROM contact_messages WHERE is_read = 0")['count'];

// Recent activity
$recentGalleries = $db->fetchAll("SELECT * FROM galleries ORDER BY created_at DESC LIMIT 5");
$recentMedia = $db->fetchAll("SELECT m.*, g.title as gallery_title FROM media_items m LEFT JOIN galleries g ON m.gallery_id = g.id ORDER BY m.created_at DESC LIMIT 5");
$recentMessages = $db->fetchAll("SELECT * FROM contact_messages ORDER BY created_at DESC LIMIT 5");

$pageTitle = "Dashboard";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Dashboard</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="../" class="btn btn-sm btn-outline-secondary" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i>View Site
                        </a>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Galleries</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $activeGalleryCount; ?> / <?php echo $galleryCount; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-images fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Media Items</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $activeMediaCount; ?> / <?php echo $mediaCount; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-photo-video fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Messages</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $messageCount; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-envelope fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Unread Messages</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $unreadMessageCount; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <!-- Recent Galleries -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Recent Galleries</h6>
                            <a href="galleries.php" class="btn btn-sm btn-primary">View All</a>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($recentGalleries)) { ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($recentGalleries as $gallery) { ?>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1"><?php echo htmlspecialchars($gallery['title']); ?></h6>
                                        <small class="text-muted"><?php echo date('M j, Y', strtotime($gallery['created_at'])); ?></small>
                                    </div>
                                    <div>
                                        <?php if ($gallery['is_active']) { ?>
                                        <span class="badge bg-success">Active</span>
                                        <?php } else { ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                        <?php } ?>
                                        <?php if ($gallery['is_featured']) { ?>
                                        <span class="badge bg-primary">Featured</span>
                                        <?php } ?>
                                    </div>
                                </div>
                                <?php } ?>
                            </div>
                            <?php } else { ?>
                            <p class="text-muted text-center">No galleries yet.</p>
                            <?php } ?>
                        </div>
                    </div>
                </div>

                <!-- Recent Messages -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Recent Messages</h6>
                            <a href="messages.php" class="btn btn-sm btn-primary">View All</a>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($recentMessages)) { ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($recentMessages as $message) { ?>
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($message['name']); ?></h6>
                                        <small><?php echo date('M j', strtotime($message['created_at'])); ?></small>
                                    </div>
                                    <p class="mb-1"><?php echo htmlspecialchars(substr($message['message'], 0, 100)) . (strlen($message['message']) > 100 ? '...' : ''); ?></p>
                                    <small class="text-muted"><?php echo htmlspecialchars($message['email']); ?></small>
                                    <?php if (!$message['is_read']) { ?>
                                    <span class="badge bg-warning ms-2">New</span>
                                    <?php } ?>
                                </div>
                                <?php } ?>
                            </div>
                            <?php } else { ?>
                            <p class="text-muted text-center">No messages yet.</p>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <a href="galleries.php?action=add" class="btn btn-primary btn-block">
                                        <i class="fas fa-plus me-2"></i>Add Gallery
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="media.php?action=upload" class="btn btn-success btn-block">
                                        <i class="fas fa-upload me-2"></i>Upload Media
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="settings.php" class="btn btn-info btn-block">
                                        <i class="fas fa-cog me-2"></i>Site Settings
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="messages.php" class="btn btn-warning btn-block">
                                        <i class="fas fa-envelope me-2"></i>View Messages
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
