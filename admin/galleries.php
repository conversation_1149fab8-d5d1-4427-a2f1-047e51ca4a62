<?php
/**
 * Admin - Gallery Management
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAuth();

$galleryModel = new Gallery();
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add' || $action === 'edit') {
        $title = sanitize($_POST['title'] ?? '');
        $description = sanitize($_POST['description'] ?? '');
        $slug = sanitize($_POST['slug'] ?? '');
        $is_featured = isset($_POST['is_featured']) ? 1 : 0;
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        $sort_order = (int)($_POST['sort_order'] ?? 0);
        
        // Validation
        $errors = [];
        if (empty($title)) $errors[] = 'Title is required';
        
        // Generate slug if empty
        if (empty($slug)) {
            $slug = $galleryModel->generateUniqueSlug($title);
        } else {
            // Check if slug is unique
            $excludeId = $action === 'edit' ? (int)$_POST['id'] : null;
            if ($galleryModel->slugExists($slug, $excludeId)) {
                $slug = $galleryModel->generateUniqueSlug($slug, $excludeId);
            }
        }
        
        if (empty($errors)) {
            $data = [
                'title' => $title,
                'slug' => $slug,
                'description' => $description,
                'is_featured' => $is_featured,
                'is_active' => $is_active,
                'sort_order' => $sort_order
            ];
            
            try {
                if ($action === 'add') {
                    $galleryModel->create($data);
                    $message = 'Gallery created successfully!';
                } else {
                    $id = (int)$_POST['id'];
                    $galleryModel->update($id, $data);
                    $message = 'Gallery updated successfully!';
                }
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'Error: ' . $e->getMessage();
                $messageType = 'error';
            }
        } else {
            $message = implode('<br>', $errors);
            $messageType = 'error';
        }
    } elseif ($action === 'delete') {
        $id = (int)$_POST['id'];
        try {
            $galleryModel->delete($id);
            $message = 'Gallery deleted successfully!';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'Error deleting gallery: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Get galleries
$galleries = $galleryModel->getAll();

// Get gallery for editing
$editGallery = null;
if (isset($_GET['edit'])) {
    $editGallery = $galleryModel->getById((int)$_GET['edit']);
}

$pageTitle = "Galleries";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Galleries</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#galleryModal">
                        <i class="fas fa-plus me-1"></i>Add Gallery
                    </button>
                </div>
            </div>

            <!-- Alert Container -->
            <div class="alert-container">
                <?php if (!empty($message)) { ?>
                <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php } ?>
            </div>

            <!-- Galleries Table -->
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">All Galleries</h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($galleries)) { ?>
                    <div class="table-responsive">
                        <table class="table table-bordered data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Title</th>
                                    <th>Slug</th>
                                    <th>Media Count</th>
                                    <th>Featured</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($galleries as $gallery) { ?>
                                <tr>
                                    <td><?php echo $gallery['id']; ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($gallery['title']); ?></strong>
                                        <?php if (!empty($gallery['description'])) { ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars(substr($gallery['description'], 0, 50)) . (strlen($gallery['description']) > 50 ? '...' : ''); ?></small>
                                        <?php } ?>
                                    </td>
                                    <td>
                                        <code><?php echo htmlspecialchars($gallery['slug']); ?></code>
                                        <br><a href="../gallery/<?php echo htmlspecialchars($gallery['slug']); ?>" target="_blank" class="small">
                                            <i class="fas fa-external-link-alt"></i> View
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo $gallery['media_count'] ?? 0; ?></span>
                                    </td>
                                    <td>
                                        <?php if ($gallery['is_featured']) { ?>
                                        <span class="badge bg-primary">Featured</span>
                                        <?php } else { ?>
                                        <span class="badge bg-secondary">Regular</span>
                                        <?php } ?>
                                    </td>
                                    <td>
                                        <?php if ($gallery['is_active']) { ?>
                                        <span class="badge bg-success">Active</span>
                                        <?php } else { ?>
                                        <span class="badge bg-danger">Inactive</span>
                                        <?php } ?>
                                    </td>
                                    <td><?php echo date('M j, Y', strtotime($gallery['created_at'])); ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary btn-edit" 
                                                    data-gallery='<?php echo json_encode($gallery); ?>'>
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <a href="media.php?gallery_id=<?php echo $gallery['id']; ?>" class="btn btn-outline-info">
                                                <i class="fas fa-images"></i>
                                            </a>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="id" value="<?php echo $gallery['id']; ?>">
                                                <button type="submit" class="btn btn-outline-danger btn-delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php } ?>
                            </tbody>
                        </table>
                    </div>
                    <?php } else { ?>
                    <div class="text-center py-4">
                        <i class="fas fa-images fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Galleries Found</h5>
                        <p class="text-muted">Create your first gallery to get started.</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#galleryModal">
                            <i class="fas fa-plus me-1"></i>Add Gallery
                        </button>
                    </div>
                    <?php } ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Gallery Modal -->
<div class="modal fade" id="galleryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" class="needs-validation" novalidate>
                <div class="modal-header">
                    <h5 class="modal-title">Add Gallery</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">
                    <input type="hidden" name="id" value="">
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="title" class="form-label">Title *</label>
                            <input type="text" class="form-control" name="title" required>
                            <div class="invalid-feedback">Please provide a title.</div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control" name="sort_order" value="0">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="slug" class="form-label">Slug</label>
                        <input type="text" class="form-control" name="slug" placeholder="Leave empty to auto-generate">
                        <div class="form-text">URL-friendly version of the title. Leave empty to auto-generate.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_featured" id="is_featured">
                                <label class="form-check-label" for="is_featured">
                                    Featured Gallery
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    Active
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Gallery</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Edit gallery
    $('.btn-edit').click(function() {
        const gallery = $(this).data('gallery');
        const modal = $('#galleryModal');
        
        modal.find('.modal-title').text('Edit Gallery');
        modal.find('input[name="action"]').val('edit');
        modal.find('input[name="id"]').val(gallery.id);
        modal.find('input[name="title"]').val(gallery.title);
        modal.find('input[name="slug"]').val(gallery.slug);
        modal.find('textarea[name="description"]').val(gallery.description);
        modal.find('input[name="sort_order"]').val(gallery.sort_order);
        modal.find('input[name="is_featured"]').prop('checked', gallery.is_featured == 1);
        modal.find('input[name="is_active"]').prop('checked', gallery.is_active == 1);
        
        modal.modal('show');
    });
    
    // Reset modal on close
    $('#galleryModal').on('hidden.bs.modal', function() {
        $(this).find('.modal-title').text('Add Gallery');
        $(this).find('form')[0].reset();
        $(this).find('input[name="action"]').val('add');
        $(this).find('input[name="id"]').val('');
        $(this).find('input[name="is_active"]').prop('checked', true);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
