<?php
echo "🔍 DEBUGGING ADMIN LOGIN\n";
echo "========================\n\n";

try {
    echo "1. Starting session...\n";
    session_start();
    echo "✅ Session started\n";

    echo "2. Including database config...\n";
    require_once '../config/database.php';
    echo "✅ Database config loaded\n";

    echo "3. Including functions...\n";
    require_once '../includes/functions.php';
    echo "✅ Functions loaded\n";

    echo "4. Testing database connection...\n";
    $db = Database::getInstance();
    echo "✅ Database connected\n";

    echo "5. Testing isLoggedIn function...\n";
    if (function_exists('isLoggedIn')) {
        $loggedIn = isLoggedIn();
        echo "✅ isLoggedIn function works: " . ($loggedIn ? 'true' : 'false') . "\n";
    } else {
        echo "❌ isLoggedIn function not found\n";
    }

    echo "6. Testing admin user query...\n";
    $admin = $db->fetchOne("SELECT * FROM users WHERE email = ? AND role = 'admin'", ['<EMAIL>']);
    if ($admin) {
        echo "✅ Admin user found: {$admin['email']}\n";
    } else {
        echo "❌ Admin user not found\n";
    }

    echo "7. Testing getSetting function...\n";
    if (function_exists('getSetting')) {
        $siteName = getSetting('site_title', 'Default');
        echo "✅ getSetting works: $siteName\n";
    } else {
        echo "❌ getSetting function not found\n";
    }

    echo "\n🎯 All basic components working!\n";
    echo "The issue might be in the HTML/CSS part of login.php\n";

} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . "\n";
    echo "📍 Line: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "❌ FATAL ERROR: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . "\n";
    echo "📍 Line: " . $e->getLine() . "\n";
}
?>
