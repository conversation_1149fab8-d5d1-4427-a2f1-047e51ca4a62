<?php
/**
 * Client Management System
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance();
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_client':
                try {
                    // Create user account
                    $password = bin2hex(random_bytes(8)); // Generate random password
                    $passwordHash = password_hash($password, PASSWORD_DEFAULT);
                    
                    $db->query("
                        INSERT INTO users (username, email, password_hash, first_name, last_name, role, status) 
                        VALUES (?, ?, ?, ?, ?, 'client', 'active')
                    ", [
                        $_POST['email'], // Use email as username
                        $_POST['email'],
                        $passwordHash,
                        $_POST['first_name'],
                        $_POST['last_name']
                    ]);
                    
                    $userId = $db->getConnection()->lastInsertId();
                    
                    // Create client profile
                    $db->query("
                        INSERT INTO client_profiles (user_id, phone, address_line1, city, state, zip_code, preferred_contact, referral_source) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ", [
                        $userId,
                        $_POST['phone'] ?? '',
                        $_POST['address'] ?? '',
                        $_POST['city'] ?? '',
                        $_POST['state'] ?? '',
                        $_POST['zip_code'] ?? '',
                        $_POST['preferred_contact'] ?? 'email',
                        $_POST['referral_source'] ?? ''
                    ]);
                    
                    $message = "Client added successfully! Temporary password: $password";
                    $messageType = 'success';
                } catch (Exception $e) {
                    $message = "Error adding client: " . $e->getMessage();
                    $messageType = 'danger';
                }
                break;
                
            case 'update_client':
                try {
                    $db->query("
                        UPDATE users SET first_name = ?, last_name = ?, email = ?, status = ? 
                        WHERE id = ? AND role = 'client'
                    ", [
                        $_POST['first_name'],
                        $_POST['last_name'],
                        $_POST['email'],
                        $_POST['status'],
                        $_POST['client_id']
                    ]);
                    
                    $db->query("
                        UPDATE client_profiles SET phone = ?, address_line1 = ?, city = ?, state = ?, zip_code = ?, preferred_contact = ? 
                        WHERE user_id = ?
                    ", [
                        $_POST['phone'] ?? '',
                        $_POST['address'] ?? '',
                        $_POST['city'] ?? '',
                        $_POST['state'] ?? '',
                        $_POST['zip_code'] ?? '',
                        $_POST['preferred_contact'] ?? 'email',
                        $_POST['client_id']
                    ]);
                    
                    $message = "Client updated successfully!";
                    $messageType = 'success';
                } catch (Exception $e) {
                    $message = "Error updating client: " . $e->getMessage();
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// Get clients with their profiles
$clients = $db->fetchAll("
    SELECT u.*, cp.phone, cp.address_line1, cp.city, cp.state, cp.zip_code, cp.preferred_contact, cp.referral_source,
           COUNT(DISTINCT b.id) as booking_count,
           COUNT(DISTINCT o.id) as order_count,
           COALESCE(SUM(o.total_amount), 0) as total_spent
    FROM users u 
    LEFT JOIN client_profiles cp ON u.id = cp.user_id 
    LEFT JOIN bookings b ON u.id = b.client_id 
    LEFT JOIN orders o ON u.id = o.client_id AND o.payment_status = 'paid'
    WHERE u.role = 'client' 
    GROUP BY u.id 
    ORDER BY u.created_at DESC
");

$pageTitle = "Client Management";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Client Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addClientModal">
                        <i class="fas fa-plus me-1"></i>Add New Client
                    </button>
                </div>
            </div>

            <?php if ($message) { ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php } ?>

            <!-- Clients Table -->
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">All Clients</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="clientsTable">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Location</th>
                                    <th>Status</th>
                                    <th>Bookings</th>
                                    <th>Orders</th>
                                    <th>Total Spent</th>
                                    <th>Joined</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($clients as $client) { ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($client['first_name'] . ' ' . $client['last_name']); ?></td>
                                    <td><?php echo htmlspecialchars($client['email']); ?></td>
                                    <td><?php echo htmlspecialchars($client['phone'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars(($client['city'] ?? '') . ', ' . ($client['state'] ?? '')); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $client['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo ucfirst($client['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo $client['booking_count']; ?></td>
                                    <td><?php echo $client['order_count']; ?></td>
                                    <td>$<?php echo number_format($client['total_spent'], 2); ?></td>
                                    <td><?php echo date('M j, Y', strtotime($client['created_at'])); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="editClient(<?php echo $client['id']; ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <a href="client-details.php?id=<?php echo $client['id']; ?>" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php } ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Add Client Modal -->
<div class="modal fade" id="addClientModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Client</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_client">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">Address</label>
                        <input type="text" class="form-control" id="address" name="address">
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="city" class="form-label">City</label>
                                <input type="text" class="form-control" id="city" name="city">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="state" class="form-label">State</label>
                                <input type="text" class="form-control" id="state" name="state">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="zip_code" class="form-label">ZIP Code</label>
                                <input type="text" class="form-control" id="zip_code" name="zip_code">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="preferred_contact" class="form-label">Preferred Contact</label>
                                <select class="form-select" id="preferred_contact" name="preferred_contact">
                                    <option value="email">Email</option>
                                    <option value="phone">Phone</option>
                                    <option value="text">Text</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="referral_source" class="form-label">How did they find us?</label>
                                <input type="text" class="form-control" id="referral_source" name="referral_source" placeholder="Google, referral, etc.">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Client</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Initialize DataTable
$(document).ready(function() {
    $('#clientsTable').DataTable({
        "pageLength": 25,
        "order": [[ 8, "desc" ]], // Sort by joined date
        "columnDefs": [
            { "orderable": false, "targets": 9 } // Disable sorting on actions column
        ]
    });
});

function editClient(clientId) {
    // TODO: Implement edit client functionality
    alert('Edit client functionality coming soon!');
}
</script>

<?php include 'includes/footer.php'; ?>
