<?php
/**
 * Admin - Performance Monitoring and Optimization
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/image-optimizer.php';

requireAuth();

$db = Database::getInstance();
$message = '';
$messageType = '';

// Handle optimization actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'optimize_images') {
        try {
            $uploadDir = '../uploads/images/';
            $results = ImageOptimizer::batchOptimize($uploadDir, 85, 1920, 1080);
            
            $totalSavings = array_sum(array_column($results, 'savings'));
            $optimizedCount = count($results);
            
            $message = "Optimized $optimizedCount images. Total space saved: " . formatFileSize($totalSavings);
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'Error optimizing images: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Get performance statistics
$stats = [
    'total_galleries' => $db->fetchOne("SELECT COUNT(*) as count FROM galleries")['count'],
    'total_media' => $db->fetchOne("SELECT COUNT(*) as count FROM media_items")['count'],
    'total_images' => $db->fetchOne("SELECT COUNT(*) as count FROM media_items WHERE file_type = 'image'")['count'],
    'total_videos' => $db->fetchOne("SELECT COUNT(*) as count FROM media_items WHERE file_type = 'video'")['count'],
    'total_messages' => $db->fetchOne("SELECT COUNT(*) as count FROM contact_messages")['count']
];

// Calculate storage usage
$uploadDir = '../uploads/';
$totalSize = 0;
$fileCount = 0;

if (is_dir($uploadDir)) {
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($uploadDir));
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $totalSize += $file->getSize();
            $fileCount++;
        }
    }
}

// Get largest files
$largeFiles = [];
if (is_dir($uploadDir)) {
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($uploadDir));
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $largeFiles[] = [
                'path' => str_replace('../', '', $file->getPathname()),
                'size' => $file->getSize(),
                'name' => $file->getFilename()
            ];
        }
    }
    
    // Sort by size descending
    usort($largeFiles, function($a, $b) {
        return $b['size'] - $a['size'];
    });
    
    $largeFiles = array_slice($largeFiles, 0, 10); // Top 10 largest files
}

$pageTitle = "Performance";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Performance Monitoring</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="optimize_images">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-compress-alt me-1"></i>Optimize Images
                        </button>
                    </form>
                </div>
            </div>

            <!-- Alert Container -->
            <div class="alert-container">
                <?php if (!empty($message)) { ?>
                <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php } ?>
            </div>

            <!-- Performance Statistics -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Storage Used</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo formatFileSize($totalSize); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-hdd fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Files</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $fileCount; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-file fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Images</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['total_images']; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-image fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Videos</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['total_videos']; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-video fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Tools -->
            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-tools me-2"></i>Optimization Tools
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="card border-left-primary h-100">
                                        <div class="card-body">
                                            <h6><i class="fas fa-compress-alt text-primary me-2"></i>Image Optimization</h6>
                                            <p class="small text-muted">Compress and resize images to improve loading times.</p>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="optimize_images">
                                                <button type="submit" class="btn btn-sm btn-primary">Optimize Now</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="card border-left-success h-100">
                                        <div class="card-body">
                                            <h6><i class="fas fa-broom text-success me-2"></i>Cache Management</h6>
                                            <p class="small text-muted">Clear browser cache and optimize delivery.</p>
                                            <button type="button" class="btn btn-sm btn-success" onclick="clearCache()">Clear Cache</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="mt-3">Performance Features</h6>
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Image Lazy Loading
                                            <span class="badge bg-success">Enabled</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            CSS/JS Compression
                                            <span class="badge bg-success">Enabled</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Browser Caching
                                            <span class="badge bg-success">Enabled</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            WebP Support
                                            <span class="badge bg-<?php echo ImageOptimizer::isWebPSupported() ? 'success' : 'warning'; ?>">
                                                <?php echo ImageOptimizer::isWebPSupported() ? 'Available' : 'Not Available'; ?>
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 mb-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-chart-pie me-2"></i>Storage Breakdown
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="text-center">
                                <div class="mb-3">
                                    <h4 class="text-primary"><?php echo formatFileSize($totalSize); ?></h4>
                                    <small class="text-muted">Total Storage Used</small>
                                </div>
                                
                                <div class="progress mb-3">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: 70%"></div>
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 20%"></div>
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 10%"></div>
                                </div>
                                
                                <div class="row text-center">
                                    <div class="col-4">
                                        <small class="text-primary">Images</small>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-success">Videos</small>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-warning">Other</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Largest Files -->
            <?php if (!empty($largeFiles)) { ?>
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-weight-hanging me-2"></i>Largest Files
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>File</th>
                                    <th>Size</th>
                                    <th>Type</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($largeFiles as $file) { 
                                    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                                    $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
                                ?>
                                <tr>
                                    <td>
                                        <small><?php echo htmlspecialchars($file['name']); ?></small>
                                    </td>
                                    <td><?php echo formatFileSize($file['size']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $isImage ? 'primary' : 'success'; ?>">
                                            <?php echo $isImage ? 'Image' : 'Video'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="../<?php echo htmlspecialchars($file['path']); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php } ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php } ?>
        </main>
    </div>
</div>

<script>
function clearCache() {
    if (confirm('This will clear browser cache instructions. Continue?')) {
        // In a real implementation, this would trigger cache clearing
        alert('Cache clearing instructions sent to .htaccess');
    }
}
</script>

<?php include 'includes/footer.php'; ?>
