<?php
/**
 * Admin - Media Management
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAuth();

$mediaModel = new MediaItem();
$galleryModel = new Gallery();
$message = '';
$messageType = '';

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['files'])) {
    $galleryId = (int)$_POST['gallery_id'];
    $uploadedFiles = [];
    $errors = [];
    
    foreach ($_FILES['files']['tmp_name'] as $key => $tmpName) {
        if ($_FILES['files']['error'][$key] === UPLOAD_ERR_OK) {
            $originalName = $_FILES['files']['name'][$key];
            $fileSize = $_FILES['files']['size'][$key];
            $mimeType = $_FILES['files']['type'][$key];
            $extension = getFileExtension($originalName);
            
            // Validate file
            if (!isImage($originalName) && !isVideo($originalName)) {
                $errors[] = "Invalid file type: $originalName";
                continue;
            }
            
            if ($fileSize > MAX_FILE_SIZE) {
                $errors[] = "File too large: $originalName";
                continue;
            }
            
            // Generate unique filename
            $filename = uniqid() . '_' . time() . '.' . $extension;
            $uploadPath = '../' . UPLOAD_PATH;
            
            if (isImage($originalName)) {
                $filePath = $uploadPath . 'images/' . $filename;
                $thumbnailPath = $uploadPath . 'thumbnails/' . $filename;
            } else {
                $filePath = $uploadPath . 'videos/' . $filename;
                $thumbnailPath = null;
            }
            
            // Move uploaded file
            if (move_uploaded_file($tmpName, $filePath)) {
                // Get image dimensions
                $width = $height = null;
                if (isImage($originalName)) {
                    $imageInfo = getimagesize($filePath);
                    if ($imageInfo) {
                        $width = $imageInfo[0];
                        $height = $imageInfo[1];
                        
                        // Generate thumbnail
                        generateThumbnail($filePath, $thumbnailPath);
                    }
                }
                
                // Save to database
                $mediaData = [
                    'gallery_id' => $galleryId,
                    'title' => pathinfo($originalName, PATHINFO_FILENAME),
                    'filename' => $filename,
                    'original_filename' => $originalName,
                    'file_path' => str_replace('../', '', $filePath),
                    'thumbnail_path' => $thumbnailPath ? str_replace('../', '', $thumbnailPath) : null,
                    'file_type' => isImage($originalName) ? 'image' : 'video',
                    'file_size' => $fileSize,
                    'mime_type' => $mimeType,
                    'width' => $width,
                    'height' => $height
                ];
                
                try {
                    $mediaModel->create($mediaData);
                    $uploadedFiles[] = $originalName;
                } catch (Exception $e) {
                    $errors[] = "Database error for $originalName: " . $e->getMessage();
                    unlink($filePath);
                    if ($thumbnailPath && file_exists($thumbnailPath)) {
                        unlink($thumbnailPath);
                    }
                }
            } else {
                $errors[] = "Failed to upload: $originalName";
            }
        }
    }
    
    if (!empty($uploadedFiles)) {
        $message = 'Successfully uploaded: ' . implode(', ', $uploadedFiles);
        $messageType = 'success';
    }
    
    if (!empty($errors)) {
        $errorMessage = implode('<br>', $errors);
        if (!empty($message)) {
            $message .= '<br><br>Errors:<br>' . $errorMessage;
        } else {
            $message = 'Errors:<br>' . $errorMessage;
            $messageType = 'error';
        }
    }
}

// Handle other actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_FILES['files'])) {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update') {
        $id = (int)$_POST['id'];
        $data = [
            'title' => sanitize($_POST['title'] ?? ''),
            'description' => sanitize($_POST['description'] ?? ''),
            'sort_order' => (int)($_POST['sort_order'] ?? 0),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        try {
            $mediaModel->update($id, $data);
            $message = 'Media item updated successfully!';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'Error updating media item: ' . $e->getMessage();
            $messageType = 'error';
        }
    } elseif ($action === 'delete') {
        $id = (int)$_POST['id'];
        try {
            $mediaModel->delete($id);
            $message = 'Media item deleted successfully!';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'Error deleting media item: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Get filter parameters
$galleryFilter = isset($_GET['gallery_id']) ? (int)$_GET['gallery_id'] : null;

// Get media items
$mediaItems = $mediaModel->getAll($galleryFilter);

// Get galleries for filter
$galleries = $galleryModel->getAll();

$pageTitle = "Media Items";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Media Items</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                        <i class="fas fa-upload me-1"></i>Upload Media
                    </button>
                </div>
            </div>

            <!-- Alert Container -->
            <div class="alert-container">
                <?php if (!empty($message)) { ?>
                <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php } ?>
            </div>

            <!-- Filters -->
            <div class="card shadow mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="gallery_id" class="form-label">Filter by Gallery</label>
                            <select class="form-select" name="gallery_id" onchange="this.form.submit()">
                                <option value="">All Galleries</option>
                                <?php foreach ($galleries as $gallery) { ?>
                                <option value="<?php echo $gallery['id']; ?>" <?php echo $galleryFilter == $gallery['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($gallery['title']); ?>
                                </option>
                                <?php } ?>
                            </select>
                        </div>
                        <div class="col-md-8 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">Apply Filter</button>
                            <a href="media.php" class="btn btn-outline-secondary">Clear</a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Media Items -->
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        Media Items 
                        <?php if ($galleryFilter) {
                            $selectedGallery = array_filter($galleries, function($g) use ($galleryFilter) { return $g['id'] == $galleryFilter; });
                            if (!empty($selectedGallery)) {
                                echo '- ' . htmlspecialchars(reset($selectedGallery)['title']);
                            }
                        } ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($mediaItems)) { ?>
                    <div class="row">
                        <?php foreach ($mediaItems as $item) { 
                            $thumbnail = !empty($item['thumbnail_path']) ? $item['thumbnail_path'] : $item['file_path'];
                            $isVideo = $item['file_type'] === 'video';
                        ?>
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card h-100">
                                <div class="position-relative">
                                    <?php if ($isVideo) { ?>
                                    <video class="card-img-top" style="height: 200px; object-fit: cover;" muted>
                                        <source src="../<?php echo htmlspecialchars($thumbnail); ?>" type="video/mp4">
                                    </video>
                                    <div class="position-absolute top-50 start-50 translate-middle">
                                        <i class="fas fa-play-circle fa-2x text-white opacity-75"></i>
                                    </div>
                                    <?php } else { ?>
                                    <img src="../<?php echo htmlspecialchars($thumbnail); ?>" class="card-img-top" 
                                         alt="<?php echo htmlspecialchars($item['title']); ?>" 
                                         style="height: 200px; object-fit: cover;">
                                    <?php } ?>
                                    
                                    <!-- Status badges -->
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <?php if (!$item['is_active']) { ?>
                                        <span class="badge bg-danger">Inactive</span>
                                        <?php } ?>
                                        <span class="badge bg-<?php echo $isVideo ? 'info' : 'success'; ?>">
                                            <?php echo $isVideo ? 'Video' : 'Image'; ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="card-body">
                                    <h6 class="card-title"><?php echo htmlspecialchars($item['title'] ?: 'Untitled'); ?></h6>
                                    <p class="card-text small text-muted">
                                        Gallery: <?php echo htmlspecialchars($item['gallery_title']); ?><br>
                                        Size: <?php echo formatFileSize($item['file_size']); ?>
                                        <?php if ($item['width'] && $item['height']) { ?>
                                        <br>Dimensions: <?php echo $item['width']; ?>×<?php echo $item['height']; ?>
                                        <?php } ?>
                                    </p>
                                </div>
                                
                                <div class="card-footer">
                                    <div class="btn-group btn-group-sm w-100">
                                        <button type="button" class="btn btn-outline-primary btn-edit" 
                                                data-item='<?php echo json_encode($item); ?>'>
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <a href="../<?php echo htmlspecialchars($item['file_path']); ?>" 
                                           target="_blank" class="btn btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="id" value="<?php echo $item['id']; ?>">
                                            <button type="submit" class="btn btn-outline-danger btn-delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php } ?>
                    </div>
                    <?php } else { ?>
                    <div class="text-center py-4">
                        <i class="fas fa-photo-video fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Media Items Found</h5>
                        <p class="text-muted">Upload your first media files to get started.</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                            <i class="fas fa-upload me-1"></i>Upload Media
                        </button>
                    </div>
                    <?php } ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-header">
                    <h5 class="modal-title">Upload Media</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="gallery_id" class="form-label">Gallery *</label>
                        <select class="form-select" name="gallery_id" required>
                            <option value="">Select Gallery</option>
                            <?php foreach ($galleries as $gallery) { ?>
                            <option value="<?php echo $gallery['id']; ?>" <?php echo $galleryFilter == $gallery['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($gallery['title']); ?>
                            </option>
                            <?php } ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="files" class="form-label">Select Files *</label>
                        <input type="file" class="form-control file-input" name="files[]" multiple 
                               accept="image/*,video/*" required>
                        <div class="form-text">
                            Supported formats: JPG, PNG, GIF, WebP, MP4, WebM, MOV<br>
                            Maximum file size: <?php echo formatFileSize(MAX_FILE_SIZE); ?>
                        </div>
                    </div>
                    
                    <div class="file-preview mt-3"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Upload Files</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Media Item</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" value="">
                    
                    <div class="mb-3">
                        <label for="edit_title" class="form-label">Title</label>
                        <input type="text" class="form-control" name="title">
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control" name="sort_order" value="0">
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" name="is_active" id="edit_is_active">
                                <label class="form-check-label" for="edit_is_active">
                                    Active
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Edit media item
    $('.btn-edit').click(function() {
        const item = $(this).data('item');
        const modal = $('#editModal');
        
        modal.find('input[name="id"]').val(item.id);
        modal.find('input[name="title"]').val(item.title);
        modal.find('textarea[name="description"]').val(item.description);
        modal.find('input[name="sort_order"]').val(item.sort_order);
        modal.find('input[name="is_active"]').prop('checked', item.is_active == 1);
        
        modal.modal('show');
    });
});
</script>

<?php include 'includes/footer.php'; ?>
