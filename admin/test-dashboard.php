<?php
/**
 * Simple Test Dashboard
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Dashboard</title></head><body>";
echo "<h1>🎉 Admin Dashboard Working!</h1>";
echo "<p>✅ You are successfully logged in to the admin panel!</p>";

try {
    $db = Database::getInstance();
    
    // Test basic queries
    $galleryCount = $db->fetchOne("SELECT COUNT(*) as count FROM galleries")['count'];
    echo "<p>📁 Galleries: $galleryCount</p>";
    
    $userCount = $db->fetchOne("SELECT COUNT(*) as count FROM users")['count'];
    echo "<p>👥 Users: $userCount</p>";
    
    $bookingCount = $db->fetchOne("SELECT COUNT(*) as count FROM bookings")['count'];
    echo "<p>📅 Bookings: $bookingCount</p>";
    
    echo "<hr>";
    echo "<h2>🔗 Navigation Links:</h2>";
    echo "<ul>";
    echo "<li><a href='index.php'>Main Dashboard</a></li>";
    echo "<li><a href='clients.php'>Client Management</a></li>";
    echo "<li><a href='upload-photos.php'>Upload Photos</a></li>";
    echo "<li><a href='bookings.php'>Booking Management</a></li>";
    echo "<li><a href='galleries.php'>Gallery Management</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p>❌ Database Error: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
