<?php
echo "🧪 ADMIN DIRECTORY TEST\n";
echo "======================\n\n";

echo "✅ PHP is working in admin directory\n";
echo "📁 Current directory: " . __DIR__ . "\n";
echo "📄 Current file: " . __FILE__ . "\n";

// Test if we can include the database config
echo "\n🔍 Testing includes:\n";

if (file_exists('../config/database.php')) {
    echo "✅ Database config file exists\n";
    try {
        require_once '../config/database.php';
        echo "✅ Database config loaded\n";
        
        $db = Database::getInstance();
        echo "✅ Database connection successful\n";
    } catch (Exception $e) {
        echo "❌ Database error: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ Database config file not found\n";
}

if (file_exists('../includes/functions.php')) {
    echo "✅ Functions file exists\n";
    try {
        require_once '../includes/functions.php';
        echo "✅ Functions loaded\n";
    } catch (Exception $e) {
        echo "❌ Functions error: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ Functions file not found\n";
}

echo "\n🎯 Admin directory is accessible!\n";
?>
