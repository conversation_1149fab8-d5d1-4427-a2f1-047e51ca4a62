<?php
/**
 * Admin - Site Settings Management
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAuth();

$settingModel = new SiteSetting();
$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $settings = $_POST['settings'] ?? [];
    
    try {
        foreach ($settings as $key => $value) {
            $settingModel->set($key, $value);
        }
        
        // Clear cache
        SiteSetting::clearCache();
        
        $message = 'Settings updated successfully!';
        $messageType = 'success';
    } catch (Exception $e) {
        $message = 'Error updating settings: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// Get grouped settings
$groupedSettings = $settingModel->getGrouped();

$pageTitle = "Settings";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Site Settings</h1>
            </div>

            <!-- Alert Container -->
            <div class="alert-container">
                <?php if (!empty($message)) { ?>
                <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php } ?>
            </div>

            <form method="POST" class="needs-validation" novalidate>
                <div class="row">
                    <!-- General Settings -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-cog me-2"></i>General Settings
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php foreach ($groupedSettings['general'] as $setting) { ?>
                                <div class="mb-3">
                                    <label for="<?php echo $setting['setting_key']; ?>" class="form-label">
                                        <?php echo ucwords(str_replace('_', ' ', $setting['setting_key'])); ?>
                                    </label>
                                    <?php if ($setting['setting_type'] === 'textarea') { ?>
                                    <textarea class="form-control" name="settings[<?php echo $setting['setting_key']; ?>]" 
                                              id="<?php echo $setting['setting_key']; ?>" rows="3"><?php echo htmlspecialchars($setting['setting_value']); ?></textarea>
                                    <?php } else { ?>
                                    <input type="text" class="form-control" name="settings[<?php echo $setting['setting_key']; ?>]" 
                                           id="<?php echo $setting['setting_key']; ?>" value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                    <?php } ?>
                                    <?php if (!empty($setting['description'])) { ?>
                                    <div class="form-text"><?php echo htmlspecialchars($setting['description']); ?></div>
                                    <?php } ?>
                                </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                    <!-- Hero Section Settings -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-image me-2"></i>Hero Section
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php foreach ($groupedSettings['hero'] as $setting) { ?>
                                <div class="mb-3">
                                    <label for="<?php echo $setting['setting_key']; ?>" class="form-label">
                                        <?php echo ucwords(str_replace(['hero_', '_'], ['', ' '], $setting['setting_key'])); ?>
                                    </label>
                                    <?php if ($setting['setting_type'] === 'textarea') { ?>
                                    <textarea class="form-control" name="settings[<?php echo $setting['setting_key']; ?>]" 
                                              id="<?php echo $setting['setting_key']; ?>" rows="2"><?php echo htmlspecialchars($setting['setting_value']); ?></textarea>
                                    <?php } else { ?>
                                    <input type="text" class="form-control" name="settings[<?php echo $setting['setting_key']; ?>]" 
                                           id="<?php echo $setting['setting_key']; ?>" value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                    <?php } ?>
                                    <?php if (!empty($setting['description'])) { ?>
                                    <div class="form-text"><?php echo htmlspecialchars($setting['description']); ?></div>
                                    <?php } ?>
                                </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-envelope me-2"></i>Contact Information
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php foreach ($groupedSettings['contact'] as $setting) { ?>
                                <div class="mb-3">
                                    <label for="<?php echo $setting['setting_key']; ?>" class="form-label">
                                        <?php echo ucwords(str_replace(['contact_', '_'], ['', ' '], $setting['setting_key'])); ?>
                                    </label>
                                    <?php if ($setting['setting_type'] === 'textarea') { ?>
                                    <textarea class="form-control" name="settings[<?php echo $setting['setting_key']; ?>]" 
                                              id="<?php echo $setting['setting_key']; ?>" rows="3"><?php echo htmlspecialchars($setting['setting_value']); ?></textarea>
                                    <?php } else { ?>
                                    <input type="text" class="form-control" name="settings[<?php echo $setting['setting_key']; ?>]" 
                                           id="<?php echo $setting['setting_key']; ?>" value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                    <?php } ?>
                                    <?php if (!empty($setting['description'])) { ?>
                                    <div class="form-text"><?php echo htmlspecialchars($setting['description']); ?></div>
                                    <?php } ?>
                                </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-share-alt me-2"></i>Social Media
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php foreach ($groupedSettings['social'] as $setting) { ?>
                                <div class="mb-3">
                                    <label for="<?php echo $setting['setting_key']; ?>" class="form-label">
                                        <i class="fab fa-<?php echo str_replace('social_', '', $setting['setting_key']); ?> me-2"></i>
                                        <?php echo ucwords(str_replace(['social_', '_'], ['', ' '], $setting['setting_key'])); ?> URL
                                    </label>
                                    <input type="url" class="form-control" name="settings[<?php echo $setting['setting_key']; ?>]" 
                                           id="<?php echo $setting['setting_key']; ?>" value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                           placeholder="https://<?php echo str_replace('social_', '', $setting['setting_key']); ?>.com/username">
                                    <?php if (!empty($setting['description'])) { ?>
                                    <div class="form-text"><?php echo htmlspecialchars($setting['description']); ?></div>
                                    <?php } ?>
                                </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                    <!-- SEO Settings -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-search me-2"></i>SEO & Analytics
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php foreach ($groupedSettings['seo'] as $setting) { ?>
                                <div class="mb-3">
                                    <label for="<?php echo $setting['setting_key']; ?>" class="form-label">
                                        <?php echo ucwords(str_replace('_', ' ', $setting['setting_key'])); ?>
                                    </label>
                                    <?php if ($setting['setting_type'] === 'textarea') { ?>
                                    <textarea class="form-control" name="settings[<?php echo $setting['setting_key']; ?>]" 
                                              id="<?php echo $setting['setting_key']; ?>" rows="4"><?php echo htmlspecialchars($setting['setting_value']); ?></textarea>
                                    <?php } else { ?>
                                    <input type="text" class="form-control" name="settings[<?php echo $setting['setting_key']; ?>]" 
                                           id="<?php echo $setting['setting_key']; ?>" value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                    <?php } ?>
                                    <?php if (!empty($setting['description'])) { ?>
                                    <div class="form-text"><?php echo htmlspecialchars($setting['description']); ?></div>
                                    <?php } ?>
                                </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                    <!-- Other Settings -->
                    <?php if (!empty($groupedSettings['other'])) { ?>
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-ellipsis-h me-2"></i>Other Settings
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php foreach ($groupedSettings['other'] as $setting) { ?>
                                <div class="mb-3">
                                    <label for="<?php echo $setting['setting_key']; ?>" class="form-label">
                                        <?php echo ucwords(str_replace('_', ' ', $setting['setting_key'])); ?>
                                    </label>
                                    <?php if ($setting['setting_type'] === 'textarea') { ?>
                                    <textarea class="form-control" name="settings[<?php echo $setting['setting_key']; ?>]" 
                                              id="<?php echo $setting['setting_key']; ?>" rows="3"><?php echo htmlspecialchars($setting['setting_value']); ?></textarea>
                                    <?php } else { ?>
                                    <input type="text" class="form-control" name="settings[<?php echo $setting['setting_key']; ?>]" 
                                           id="<?php echo $setting['setting_key']; ?>" value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                    <?php } ?>
                                    <?php if (!empty($setting['description'])) { ?>
                                    <div class="form-text"><?php echo htmlspecialchars($setting['description']); ?></div>
                                    <?php } ?>
                                </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                    <?php } ?>
                </div>

                <!-- Save Button -->
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-body text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save me-2"></i>Save All Settings
                                </button>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Changes will be applied immediately to the website.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </main>
    </div>
</div>

<script>
$(document).ready(function() {
    // Auto-save indication
    $('form').submit(function() {
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        
        submitBtn.html('<span class="spinner-border spinner-border-sm me-2"></span>Saving...');
        submitBtn.prop('disabled', true);
        
        // Re-enable after form submission
        setTimeout(function() {
            submitBtn.html(originalText);
            submitBtn.prop('disabled', false);
        }, 2000);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
