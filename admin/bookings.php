<?php
/**
 * Booking Management System
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance();
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_booking':
                try {
                    $db->query("
                        INSERT INTO bookings (client_id, session_type, package_type, session_date, session_time, 
                                            duration_hours, location, total_amount, deposit_amount, balance_due, 
                                            special_requests, status) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')
                    ", [
                        $_POST['client_id'],
                        $_POST['session_type'],
                        $_POST['package_type'],
                        $_POST['session_date'],
                        $_POST['session_time'],
                        $_POST['duration_hours'],
                        $_POST['location'],
                        $_POST['total_amount'],
                        $_POST['deposit_amount'],
                        $_POST['total_amount'] - $_POST['deposit_amount'],
                        $_POST['special_requests']
                    ]);
                    
                    $message = "Booking added successfully!";
                    $messageType = 'success';
                } catch (Exception $e) {
                    $message = "Error adding booking: " . $e->getMessage();
                    $messageType = 'danger';
                }
                break;
                
            case 'update_status':
                try {
                    $db->query("UPDATE bookings SET status = ? WHERE id = ?", [
                        $_POST['status'],
                        $_POST['booking_id']
                    ]);
                    
                    $message = "Booking status updated successfully!";
                    $messageType = 'success';
                } catch (Exception $e) {
                    $message = "Error updating booking: " . $e->getMessage();
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// Get bookings with client information
$bookings = $db->fetchAll("
    SELECT b.*, u.first_name, u.last_name, u.email, u.phone 
    FROM bookings b 
    JOIN users u ON b.client_id = u.id 
    ORDER BY b.session_date DESC, b.session_time DESC
");

// Get clients for dropdown
$clients = $db->fetchAll("
    SELECT u.id, u.first_name, u.last_name, u.email 
    FROM users u 
    WHERE u.role = 'client' AND u.status = 'active' 
    ORDER BY u.first_name, u.last_name
");

$pageTitle = "Booking Management";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Booking Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBookingModal">
                        <i class="fas fa-plus me-1"></i>Add New Booking
                    </button>
                </div>
            </div>

            <?php if ($message) { ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php } ?>

            <!-- Booking Statistics -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Pending Bookings</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo count(array_filter($bookings, fn($b) => $b['status'] === 'pending')); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Confirmed</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo count(array_filter($bookings, fn($b) => $b['status'] === 'confirmed')); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">This Month</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php 
                                        $thisMonth = count(array_filter($bookings, function($b) {
                                            return date('Y-m', strtotime($b['session_date'])) === date('Y-m');
                                        }));
                                        echo $thisMonth;
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Total Revenue</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        $<?php 
                                        $totalRevenue = array_sum(array_column(
                                            array_filter($bookings, fn($b) => $b['status'] === 'completed'), 
                                            'total_amount'
                                        ));
                                        echo number_format($totalRevenue, 2);
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bookings Table -->
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">All Bookings</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="bookingsTable">
                            <thead>
                                <tr>
                                    <th>Client</th>
                                    <th>Session Type</th>
                                    <th>Package</th>
                                    <th>Date & Time</th>
                                    <th>Duration</th>
                                    <th>Location</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($bookings as $booking) { ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($booking['first_name'] . ' ' . $booking['last_name']); ?></strong><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($booking['email']); ?></small>
                                    </td>
                                    <td><?php echo ucfirst($booking['session_type']); ?></td>
                                    <td><?php echo htmlspecialchars($booking['package_type'] ?? 'Custom'); ?></td>
                                    <td>
                                        <?php echo date('M j, Y', strtotime($booking['session_date'])); ?><br>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($booking['session_time'])); ?></small>
                                    </td>
                                    <td><?php echo $booking['duration_hours']; ?> hrs</td>
                                    <td><?php echo htmlspecialchars($booking['location'] ?? 'TBD'); ?></td>
                                    <td>
                                        $<?php echo number_format($booking['total_amount'], 2); ?><br>
                                        <?php if ($booking['deposit_paid']) { ?>
                                        <small class="text-success">Deposit Paid</small>
                                        <?php } else { ?>
                                        <small class="text-warning">Deposit Pending</small>
                                        <?php } ?>
                                    </td>
                                    <td>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="update_status">
                                            <input type="hidden" name="booking_id" value="<?php echo $booking['id']; ?>">
                                            <select name="status" class="form-select form-select-sm" onchange="this.form.submit()">
                                                <option value="pending" <?php echo $booking['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                                <option value="confirmed" <?php echo $booking['status'] === 'confirmed' ? 'selected' : ''; ?>>Confirmed</option>
                                                <option value="completed" <?php echo $booking['status'] === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                                <option value="cancelled" <?php echo $booking['status'] === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                            </select>
                                        </form>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewBooking(<?php echo $booking['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="createGallery(<?php echo $booking['id']; ?>)">
                                                <i class="fas fa-images"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php } ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Add Booking Modal -->
<div class="modal fade" id="addBookingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Booking</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_booking">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="client_id" class="form-label">Client *</label>
                                <select class="form-select" id="client_id" name="client_id" required>
                                    <option value="">Select a client...</option>
                                    <?php foreach ($clients as $client) { ?>
                                    <option value="<?php echo $client['id']; ?>">
                                        <?php echo htmlspecialchars($client['first_name'] . ' ' . $client['last_name'] . ' (' . $client['email'] . ')'); ?>
                                    </option>
                                    <?php } ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="session_type" class="form-label">Session Type *</label>
                                <select class="form-select" id="session_type" name="session_type" required>
                                    <option value="">Select type...</option>
                                    <option value="portrait">Portrait</option>
                                    <option value="wedding">Wedding</option>
                                    <option value="event">Event</option>
                                    <option value="food">Food</option>
                                    <option value="commercial">Commercial</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="session_date" class="form-label">Session Date *</label>
                                <input type="date" class="form-control" id="session_date" name="session_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="session_time" class="form-label">Session Time *</label>
                                <input type="time" class="form-control" id="session_time" name="session_time" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="duration_hours" class="form-label">Duration (hours)</label>
                                <input type="number" class="form-control" id="duration_hours" name="duration_hours" step="0.5" value="2" min="0.5">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="total_amount" class="form-label">Total Amount</label>
                                <input type="number" class="form-control" id="total_amount" name="total_amount" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="deposit_amount" class="form-label">Deposit Amount</label>
                                <input type="number" class="form-control" id="deposit_amount" name="deposit_amount" step="0.01" min="0">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="package_type" class="form-label">Package Type</label>
                        <input type="text" class="form-control" id="package_type" name="package_type" placeholder="e.g., Professional, Premium, Custom">
                    </div>
                    <div class="mb-3">
                        <label for="location" class="form-label">Location</label>
                        <input type="text" class="form-control" id="location" name="location" placeholder="Session location">
                    </div>
                    <div class="mb-3">
                        <label for="special_requests" class="form-label">Special Requests</label>
                        <textarea class="form-control" id="special_requests" name="special_requests" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Booking</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Initialize DataTable
$(document).ready(function() {
    $('#bookingsTable').DataTable({
        "pageLength": 25,
        "order": [[ 3, "desc" ]], // Sort by date
        "columnDefs": [
            { "orderable": false, "targets": 8 } // Disable sorting on actions column
        ]
    });
});

function viewBooking(bookingId) {
    // TODO: Implement view booking details
    alert('View booking details coming soon!');
}

function createGallery(bookingId) {
    // TODO: Implement create gallery for booking
    alert('Create gallery for booking coming soon!');
}
</script>

<?php include 'includes/footer.php'; ?>
