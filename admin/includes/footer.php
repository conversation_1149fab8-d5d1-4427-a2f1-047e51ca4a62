    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- Custom Admin JS -->
    <script>
        $(document).ready(function() {
            // Initialize DataTables
            $('.data-table').DataTable({
                "pageLength": 25,
                "responsive": true,
                "order": [[ 0, "desc" ]],
                "language": {
                    "search": "Search:",
                    "lengthMenu": "Show _MENU_ entries",
                    "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    }
                }
            });
            
            // Auto-hide alerts after 5 seconds
            $('.alert').delay(5000).fadeOut();
            
            // Confirm delete actions
            $('.btn-delete').click(function(e) {
                if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                    e.preventDefault();
                }
            });
            
            // Toggle active status
            $('.toggle-active').change(function() {
                const checkbox = $(this);
                const itemId = checkbox.data('id');
                const itemType = checkbox.data('type');
                const isActive = checkbox.is(':checked') ? 1 : 0;
                
                $.ajax({
                    url: 'ajax/toggle-active.php',
                    method: 'POST',
                    data: {
                        id: itemId,
                        type: itemType,
                        active: isActive
                    },
                    success: function(response) {
                        if (response.success) {
                            showAlert('success', 'Status updated successfully');
                        } else {
                            showAlert('error', 'Failed to update status');
                            checkbox.prop('checked', !isActive);
                        }
                    },
                    error: function() {
                        showAlert('error', 'Failed to update status');
                        checkbox.prop('checked', !isActive);
                    }
                });
            });
            
            // File upload preview
            $('.file-input').change(function() {
                const input = this;
                const preview = $(input).siblings('.file-preview');
                
                if (input.files && input.files[0]) {
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        if (input.files[0].type.startsWith('image/')) {
                            preview.html('<img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">');
                        } else if (input.files[0].type.startsWith('video/')) {
                            preview.html('<video controls style="max-width: 200px; max-height: 200px;"><source src="' + e.target.result + '"></video>');
                        }
                    };
                    
                    reader.readAsDataURL(input.files[0]);
                }
            });
            
            // Form validation
            $('.needs-validation').submit(function(e) {
                const form = this;
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
        
        // Show alert function
        function showAlert(type, message) {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            $('.alert-container').html(alertHtml);
            $('.alert').delay(5000).fadeOut();
        }
        
        // Sortable functionality for galleries and media
        if (typeof Sortable !== 'undefined') {
            const sortableElements = document.querySelectorAll('.sortable');
            sortableElements.forEach(function(element) {
                new Sortable(element, {
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    onEnd: function(evt) {
                        const itemId = evt.item.dataset.id;
                        const newIndex = evt.newIndex;
                        const itemType = element.dataset.type;
                        
                        // Update sort order via AJAX
                        $.ajax({
                            url: 'ajax/update-sort.php',
                            method: 'POST',
                            data: {
                                id: itemId,
                                type: itemType,
                                sort_order: newIndex
                            },
                            success: function(response) {
                                if (response.success) {
                                    showAlert('success', 'Sort order updated');
                                } else {
                                    showAlert('error', 'Failed to update sort order');
                                }
                            }
                        });
                    }
                });
            });
        }
    </script>
</body>
</html>
