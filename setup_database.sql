-- MariaDB Database Setup for Kuderik Photo
-- Run this with: sudo mysql -u root < setup_database.sql

-- Create database
CREATE DATABASE IF NOT EXISTS kuderik_photo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create a dedicated user for the application
CREATE USER IF NOT EXISTS 'kuderik_user'@'localhost' IDENTIFIED BY 'kuderik_password_2024';

-- Grant privileges to the user
GRANT ALL PRIVILEGES ON kuderik_photo.* TO 'kuderik_user'@'localhost';

-- Flush privileges to ensure they take effect
FLUSH PRIVILEGES;

-- Use the database
USE kuderik_photo;

-- Admin users table
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(50),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE
);

-- Galleries table
CREATE TABLE IF NOT EXISTS galleries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) NOT NULL UNIQUE,
    description TEXT,
    cover_image VARCHAR(255),
    sort_order INT DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_slug (slug),
    INDEX idx_active (is_active),
    INDEX idx_featured (is_featured),
    INDEX idx_sort (sort_order)
);

-- Media items table (images and videos)
CREATE TABLE IF NOT EXISTS media_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    gallery_id INT,
    title VARCHAR(200),
    description TEXT,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255),
    file_path VARCHAR(500) NOT NULL,
    thumbnail_path VARCHAR(500),
    file_type ENUM('image', 'video') NOT NULL,
    file_size INT,
    mime_type VARCHAR(100),
    width INT,
    height INT,
    duration INT, -- for videos in seconds
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (gallery_id) REFERENCES galleries(id) ON DELETE CASCADE,
    INDEX idx_gallery (gallery_id),
    INDEX idx_type (file_type),
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
);

-- Site settings table
CREATE TABLE IF NOT EXISTS site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('text', 'textarea', 'image', 'video', 'boolean', 'number') DEFAULT 'text',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (setting_key)
);

-- Contact messages table
CREATE TABLE IF NOT EXISTS contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHAR(200),
    message TEXT NOT NULL,
    phone VARCHAR(20),
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_read (is_read),
    INDEX idx_created (created_at)
);

-- Insert default admin user (password: admin123)
INSERT INTO admin_users (username, email, password, first_name, last_name) 
VALUES ('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User')
ON DUPLICATE KEY UPDATE username = username;

-- Insert default site settings
INSERT INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('site_title', 'Kuderik Photo', 'text', 'Main site title'),
('site_tagline', 'Multimedia genius specializing in photography and video productions', 'text', 'Site tagline/description'),
('hero_title', 'Capturing Life\'s Moments', 'text', 'Hero section title'),
('hero_subtitle', 'Professional Photography & Video Production', 'text', 'Hero section subtitle'),
('hero_video', '', 'video', 'Hero background video (MP4)'),
('hero_image', '', 'image', 'Hero background image (fallback)'),
('about_text', 'Welcome to Kuderik Photo, where creativity meets technical excellence. We specialize in capturing life\'s most precious moments through the lens of artistic vision and professional expertise.', 'textarea', 'About section text'),
('contact_email', '<EMAIL>', 'text', 'Contact email address'),
('contact_phone', '+****************', 'text', 'Contact phone number'),
('contact_address', '123 Photography Lane, Creative City, CC 12345', 'textarea', 'Contact address'),
('social_instagram', '', 'text', 'Instagram URL'),
('social_facebook', '', 'text', 'Facebook URL'),
('social_twitter', '', 'text', 'Twitter URL'),
('social_youtube', '', 'text', 'YouTube URL'),
('google_analytics', '', 'textarea', 'Google Analytics tracking code'),
('meta_keywords', 'photography, video production, professional photographer, multimedia', 'text', 'Default meta keywords'),
('footer_text', '© 2024 Kuderik Photo. All rights reserved.', 'text', 'Footer copyright text')
ON DUPLICATE KEY UPDATE setting_key = setting_key;

-- Insert sample galleries
INSERT INTO galleries (title, slug, description, is_featured) VALUES
('Portrait Photography', 'portrait-photography', 'Professional portrait sessions capturing personality and emotion', TRUE),
('Wedding Photography', 'wedding-photography', 'Beautiful wedding moments preserved for a lifetime', TRUE),
('Food Photography', 'food-photography', 'Mouth-watering food photography for restaurants and culinary brands', TRUE),
('Product Photography', 'product-photography', 'High-quality product shots for e-commerce and marketing', TRUE),
('360 Tours', '360-tours', 'Immersive 360-degree virtual tours for businesses and real estate', TRUE),
('Real Estate Photography', 'real-estate-photography', 'Professional real estate photography showcasing properties at their best', TRUE),
('Commercial Photography', 'commercial-photography', 'Professional commercial and corporate photography services', FALSE),
('Video Productions', 'video-productions', 'Creative video content for all occasions', FALSE)
ON DUPLICATE KEY UPDATE title = title;
