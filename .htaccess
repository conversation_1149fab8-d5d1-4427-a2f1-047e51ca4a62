RewriteEngine On

# SEO-friendly folder structure
# Ensure trailing slashes for directories
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_URI} !(.*)/$
RewriteCond %{REQUEST_URI} ^/(about|services|portfolio|contact|gallery|admin)
RewriteRule ^(.*)$ /$1/ [R=301,L]

# Handle folder-based routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# About page
RewriteRule ^about/?$ about/index.php [L]

# Services page
RewriteRule ^services/?$ services/index.php [L]

# Portfolio page
RewriteRule ^portfolio/?$ portfolio/index.php [L]

# Contact page
RewriteRule ^contact/?$ contact/index.php [L]

# Gallery pages
RewriteRule ^gallery/([a-zA-Z0-9-]+)/?$ gallery/index.php [L]

# Admin pages (keep existing admin routing)
RewriteRule ^admin/?$ admin/index.php [L]
RewriteRule ^admin/(.+)$ admin/$1 [L]

# Fallback to main index for home page and other routes
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive on

    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType application/x-javascript "access plus 1 year"

    # Images
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"

    # Videos
    ExpiresByType video/mp4 "access plus 1 year"
    ExpiresByType video/webm "access plus 1 year"
    ExpiresByType video/ogg "access plus 1 year"

    # Fonts
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"

    # HTML and PHP
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType application/x-httpd-php "access plus 1 hour"
</IfModule>

# Add Cache-Control headers
<IfModule mod_headers.c>
    # Cache static assets for 1 year
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|svg|ico|woff|woff2|mp4|webm)$">
        Header set Cache-Control "public, max-age=31536000, immutable"
    </FilesMatch>

    # Cache HTML for 1 hour
    <FilesMatch "\.(html|php)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>
</IfModule>

# Protect sensitive files
<Files "*.php">
    <IfModule mod_rewrite.c>
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} -f
        RewriteRule ^(config|includes)/ - [F,L]
    </IfModule>
</Files>

# Deny access to sensitive directories
<DirectoryMatch "^.*(config|includes).*$">
    Deny from all
</DirectoryMatch>
