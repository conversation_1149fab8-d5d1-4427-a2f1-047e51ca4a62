<?php
/**
 * Kuderik Photo - Installation Test Script
 * Run this script to verify your installation is working correctly
 */

// Start output buffering to capture any errors
ob_start();
$errors = [];
$warnings = [];
$success = [];

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Kuderik Photo - Installation Test</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
</head>
<body class='bg-light'>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-lg-8'>
            <div class='card shadow'>
                <div class='card-header bg-primary text-white'>
                    <h3 class='mb-0'><i class='fas fa-camera me-2'></i>Kuderik Photo - Installation Test</h3>
                </div>
                <div class='card-body'>";

// Test 1: PHP Version
echo "<h5><i class='fas fa-code me-2'></i>PHP Environment</h5>";
if (version_compare(PHP_VERSION, '7.4.0', '>=')) {
    $success[] = "PHP Version: " . PHP_VERSION . " ✓";
} else {
    $errors[] = "PHP Version: " . PHP_VERSION . " (Requires 7.4+)";
}

// Test 2: Required Extensions
$required_extensions = ['pdo', 'pdo_mysql', 'gd', 'session'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        $success[] = "Extension $ext: Loaded ✓";
    } else {
        $errors[] = "Extension $ext: Not loaded";
    }
}

// Test 3: File Permissions
echo "<h5 class='mt-4'><i class='fas fa-folder me-2'></i>File Permissions</h5>";
$directories = [
    'uploads/',
    'uploads/images/',
    'uploads/videos/',
    'uploads/thumbnails/',
    'config/'
];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            $success[] = "Directory $dir: Writable ✓";
        } else {
            $errors[] = "Directory $dir: Not writable";
        }
    } else {
        $warnings[] = "Directory $dir: Does not exist";
    }
}

// Test 4: Configuration Files
echo "<h5 class='mt-4'><i class='fas fa-cog me-2'></i>Configuration</h5>";
$config_files = [
    'config/database.php',
    'includes/functions.php',
    'includes/router.php'
];

foreach ($config_files as $file) {
    if (file_exists($file)) {
        $success[] = "File $file: Exists ✓";
    } else {
        $errors[] = "File $file: Missing";
    }
}

// Test 5: Database Connection
echo "<h5 class='mt-4'><i class='fas fa-database me-2'></i>Database Connection</h5>";
try {
    require_once 'config/database.php';
    $db = Database::getInstance();
    $result = $db->fetchOne("SELECT 1 as test");
    if ($result && $result['test'] == 1) {
        $success[] = "Database Connection: Successful ✓";
        
        // Test tables
        $tables = ['galleries', 'media_items', 'admin_users', 'site_settings', 'contact_messages'];
        foreach ($tables as $table) {
            try {
                $count = $db->fetchOne("SELECT COUNT(*) as count FROM $table");
                $success[] = "Table $table: Exists (" . $count['count'] . " records) ✓";
            } catch (Exception $e) {
                $errors[] = "Table $table: Missing or inaccessible";
            }
        }
    } else {
        $errors[] = "Database Connection: Failed to execute test query";
    }
} catch (Exception $e) {
    $errors[] = "Database Connection: " . $e->getMessage();
}

// Test 6: URL Rewriting
echo "<h5 class='mt-4'><i class='fas fa-link me-2'></i>URL Rewriting</h5>";
if (file_exists('.htaccess')) {
    $success[] = ".htaccess file: Exists ✓";
    
    // Check if mod_rewrite is available
    if (function_exists('apache_get_modules')) {
        $modules = apache_get_modules();
        if (in_array('mod_rewrite', $modules)) {
            $success[] = "mod_rewrite: Enabled ✓";
        } else {
            $warnings[] = "mod_rewrite: May not be enabled";
        }
    } else {
        $warnings[] = "mod_rewrite: Cannot detect (function not available)";
    }
} else {
    $errors[] = ".htaccess file: Missing";
}

// Display Results
echo "<div class='mt-4'>";

if (!empty($success)) {
    echo "<div class='alert alert-success'>";
    echo "<h6><i class='fas fa-check-circle me-2'></i>Successful Tests</h6>";
    echo "<ul class='mb-0'>";
    foreach ($success as $item) {
        echo "<li>$item</li>";
    }
    echo "</ul></div>";
}

if (!empty($warnings)) {
    echo "<div class='alert alert-warning'>";
    echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>Warnings</h6>";
    echo "<ul class='mb-0'>";
    foreach ($warnings as $item) {
        echo "<li>$item</li>";
    }
    echo "</ul></div>";
}

if (!empty($errors)) {
    echo "<div class='alert alert-danger'>";
    echo "<h6><i class='fas fa-times-circle me-2'></i>Errors</h6>";
    echo "<ul class='mb-0'>";
    foreach ($errors as $item) {
        echo "<li>$item</li>";
    }
    echo "</ul></div>";
}

// Overall Status
if (empty($errors)) {
    echo "<div class='alert alert-success text-center'>";
    echo "<h4><i class='fas fa-thumbs-up me-2'></i>Installation Test Passed!</h4>";
    echo "<p class='mb-0'>Your Kuderik Photo installation appears to be working correctly.</p>";
    echo "<div class='mt-3'>";
    echo "<a href='index.php' class='btn btn-primary me-2'><i class='fas fa-home me-1'></i>View Website</a>";
    echo "<a href='admin/' class='btn btn-secondary'><i class='fas fa-cog me-1'></i>Admin Panel</a>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<div class='alert alert-danger text-center'>";
    echo "<h4><i class='fas fa-times me-2'></i>Installation Issues Found</h4>";
    echo "<p class='mb-0'>Please fix the errors above before using the website.</p>";
    echo "</div>";
}

echo "</div>"; // Close results div

// Quick Links
echo "<div class='mt-4'>";
echo "<h6>Quick Links:</h6>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<ul class='list-unstyled'>";
echo "<li><a href='install.php'><i class='fas fa-download me-1'></i>Run Installer</a></li>";
echo "<li><a href='sitemap.xml.php' target='_blank'><i class='fas fa-sitemap me-1'></i>View Sitemap</a></li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<ul class='list-unstyled'>";
echo "<li><a href='robots.txt' target='_blank'><i class='fas fa-robot me-1'></i>View Robots.txt</a></li>";
echo "<li><a href='README.md' target='_blank'><i class='fas fa-book me-1'></i>Documentation</a></li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";

// Clean up output buffer
ob_end_flush();
?>
