# PHP
*.log
*.tmp
*.cache
.env
.env.local
.env.production

# Database
*.db
*.sqlite
*.sqlite3
config/kuderik_photo.db

# Uploads and Media
uploads/*
!uploads/.gitkeep
media/*
!media/.gitkeep

# Vendor dependencies
vendor/
composer.lock

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/
*.tmp

# Logs
logs/
*.log

# Runtime data
pids
*.pid
*.seed

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Build files
dist/
build/

# Configuration files with sensitive data
config/local.php
config/production.php
