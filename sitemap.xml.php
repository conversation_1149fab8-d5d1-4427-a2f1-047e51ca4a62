<?php
/**
 * Dynamic XML Sitemap Generator
 */

header('Content-Type: application/xml; charset=utf-8');

require_once 'config/database.php';
require_once 'includes/functions.php';

$galleryModel = new Gallery();
$galleries = $galleryModel->getAllActive();

$baseUrl = getSetting('site_url', SITE_URL);
$baseUrl = rtrim($baseUrl, '/');

echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
    
    <!-- Homepage -->
    <url>
        <loc><?php echo $baseUrl; ?>/</loc>
        <lastmod><?php echo date('c'); ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority>1.0</priority>
    </url>
    
    <!-- About Page -->
    <url>
        <loc><?php echo $baseUrl; ?>/about</loc>
        <lastmod><?php echo date('c'); ?></lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>
    
    <!-- Portfolio Page -->
    <url>
        <loc><?php echo $baseUrl; ?>/portfolio</loc>
        <lastmod><?php echo date('c'); ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.9</priority>
    </url>
    
    <!-- Services Page -->
    <url>
        <loc><?php echo $baseUrl; ?>/services</loc>
        <lastmod><?php echo date('c'); ?></lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.7</priority>
    </url>
    
    <!-- Contact Page -->
    <url>
        <loc><?php echo $baseUrl; ?>/contact</loc>
        <lastmod><?php echo date('c'); ?></lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.6</priority>
    </url>
    
    <?php foreach ($galleries as $gallery) { 
        $lastmod = date('c', strtotime($gallery['updated_at'] ?: $gallery['created_at']));
        
        // Get gallery images for image sitemap
        $mediaModel = new MediaItem();
        $mediaItems = $mediaModel->getByGalleryId($gallery['id']);
        $images = array_filter($mediaItems, function($item) {
            return $item['file_type'] === 'image';
        });
    ?>
    <!-- Gallery: <?php echo htmlspecialchars($gallery['title']); ?> -->
    <url>
        <loc><?php echo $baseUrl; ?>/gallery/<?php echo htmlspecialchars($gallery['slug']); ?></loc>
        <lastmod><?php echo $lastmod; ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority><?php echo $gallery['is_featured'] ? '0.9' : '0.8'; ?></priority>
        
        <?php foreach (array_slice($images, 0, 10) as $image) { // Limit to 10 images per page ?>
        <image:image>
            <image:loc><?php echo $baseUrl; ?>/<?php echo htmlspecialchars($image['file_path']); ?></image:loc>
            <?php if (!empty($image['title'])) { ?>
            <image:title><?php echo htmlspecialchars($image['title']); ?></image:title>
            <?php } ?>
            <?php if (!empty($image['description'])) { ?>
            <image:caption><?php echo htmlspecialchars($image['description']); ?></image:caption>
            <?php } ?>
        </image:image>
        <?php } ?>
    </url>
    <?php } ?>
    
</urlset>
