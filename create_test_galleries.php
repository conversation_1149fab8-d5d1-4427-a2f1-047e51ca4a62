<?php
/**
 * Create Test Galleries for Photo Upload Testing
 */

require_once 'config/database.php';

echo "📸 CREATING TEST GALLERIES!\n";
echo "===========================\n\n";

try {
    $db = Database::getInstance();
    echo "✅ Database connection successful!\n\n";
    
    // Test galleries to create
    $testGalleries = [
        [
            'title' => 'Sarah Johnson - Portrait Session',
            'slug' => 'sarah-johnson-portraits',
            'description' => 'Professional portrait session for <PERSON>',
            'is_featured' => 0
        ],
        [
            'title' => '<PERSON> - Wedding Photography',
            'slug' => 'michael-chen-wedding',
            'description' => 'Wedding photography for <PERSON>',
            'is_featured' => 0
        ],
        [
            'title' => '<PERSON> - Event Photos',
            'slug' => 'emily-rodriguez-event',
            'description' => 'Corporate event photography for <PERSON>',
            'is_featured' => 0
        ],
        [
            'title' => 'Public Portfolio - Best Shots',
            'slug' => 'public-portfolio-best',
            'description' => 'Public gallery showcasing our best work',
            'is_featured' => 1
        ]
    ];
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($testGalleries as $gallery) {
        echo "📁 Creating gallery: {$gallery['title']}\n";
        
        try {
            // Check if gallery already exists
            $existing = $db->fetchOne("SELECT id FROM galleries WHERE slug = ?", [$gallery['slug']]);
            if ($existing) {
                echo "  ⚠️  Gallery already exists, skipping...\n";
                continue;
            }
            
            // Create gallery
            $db->query("
                INSERT INTO galleries (title, slug, description, is_featured, is_active)
                VALUES (?, ?, ?, ?, 1)
            ", [
                $gallery['title'],
                $gallery['slug'],
                $gallery['description'],
                $gallery['is_featured']
            ]);
            
            $galleryId = $db->getConnection()->lastInsertId();
            
            // Create gallery directory
            $galleryDir = "uploads/galleries/$galleryId";
            $thumbDir = "$galleryDir/thumbs";
            
            if (!file_exists($galleryDir)) {
                mkdir($galleryDir, 0755, true);
                echo "  📁 Created directory: $galleryDir\n";
            }
            
            if (!file_exists($thumbDir)) {
                mkdir($thumbDir, 0755, true);
                echo "  📁 Created directory: $thumbDir\n";
            }
            
            // Note: Gallery permissions will be set up later when enhanced schema is applied
            
            echo "  ✅ Gallery created successfully!\n";
            echo "  🆔 Gallery ID: $galleryId\n";
            echo "  🔗 Slug: {$gallery['slug']}\n\n";
            
            $successCount++;
            
        } catch (Exception $e) {
            echo "  ❌ Error creating gallery: " . $e->getMessage() . "\n\n";
            $errorCount++;
        }
    }
    
    echo "📊 GALLERY CREATION SUMMARY:\n";
    echo "============================\n";
    echo "✅ Successfully created: $successCount galleries\n";
    echo "❌ Errors: $errorCount\n\n";
    
    // Verify galleries were created
    echo "🔍 VERIFYING CREATED GALLERIES:\n";
    echo "===============================\n";
    
    $galleries = $db->fetchAll("
        SELECT * FROM galleries
        ORDER BY created_at DESC
    ");
    
    foreach ($galleries as $gallery) {
        echo "📁 {$gallery['title']}\n";
        echo "   🆔 ID: {$gallery['id']}\n";
        echo "   🔗 Slug: {$gallery['slug']}\n";
        echo "   ⭐ Featured: " . ($gallery['is_featured'] ? 'Yes' : 'No') . "\n";
        echo "   📅 Created: " . date('M j, Y', strtotime($gallery['created_at'])) . "\n\n";
    }
    
    echo "🎯 NEXT STEPS:\n";
    echo "=============\n";
    echo "1. ✅ Test galleries created!\n";
    echo "2. 🔄 Test photo upload to galleries\n";
    echo "3. 🔄 Test thumbnail generation\n";
    echo "4. 🔄 Test gallery permissions\n";
    echo "5. 🔄 Test client gallery access\n\n";
    
    echo "🚀 PHASE 3 GALLERY SETUP COMPLETE!\n";
    
} catch (Exception $e) {
    echo "❌ TEST FAILED!\n";
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Gallery Creation Test Complete!\n";
echo str_repeat("=", 50) . "\n";
?>
