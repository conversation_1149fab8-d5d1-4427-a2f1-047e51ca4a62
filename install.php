<?php
/**
 * Kuderik Photo Installation Script
 * Run this file once to set up the database
 */

// Check if already installed
if (file_exists('config/.installed')) {
    die('Installation already completed. Delete config/.installed to reinstall.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kuderik Photo - Installation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="mb-0">Kuderik Photo Installation</h3>
                    </div>
                    <div class="card-body">
                        <?php
                        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                            $host = $_POST['host'] ?? 'localhost';
                            $dbname = $_POST['dbname'] ?? 'kuderik_photo';
                            $username = $_POST['username'] ?? 'root';
                            $password = $_POST['password'] ?? '';
                            
                            try {
                                // Connect to MySQL server
                                $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
                                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                                
                                // Read and execute SQL file
                                $sql = file_get_contents('config/setup.sql');
                                
                                // Replace database name in SQL
                                $sql = str_replace('kuderik_photo', $dbname, $sql);
                                
                                // Execute SQL statements
                                $statements = explode(';', $sql);
                                foreach ($statements as $statement) {
                                    $statement = trim($statement);
                                    if (!empty($statement)) {
                                        $pdo->exec($statement);
                                    }
                                }
                                
                                // Update database config
                                $configContent = file_get_contents('config/database.php');
                                $configContent = str_replace("define('DB_HOST', 'localhost');", "define('DB_HOST', '$host');", $configContent);
                                $configContent = str_replace("define('DB_NAME', 'kuderik_photo');", "define('DB_NAME', '$dbname');", $configContent);
                                $configContent = str_replace("define('DB_USER', 'root');", "define('DB_USER', '$username');", $configContent);
                                $configContent = str_replace("define('DB_PASS', '');", "define('DB_PASS', '$password');", $configContent);
                                file_put_contents('config/database.php', $configContent);
                                
                                // Create installation marker
                                file_put_contents('config/.installed', date('Y-m-d H:i:s'));
                                
                                echo '<div class="alert alert-success">
                                        <h4>Installation Successful!</h4>
                                        <p>Database has been created and configured successfully.</p>
                                        <p><strong>Default Admin Login:</strong></p>
                                        <ul>
                                            <li>Username: admin</li>
                                            <li>Password: admin123</li>
                                        </ul>
                                        <p><a href="index.php" class="btn btn-primary">Go to Website</a> 
                                           <a href="admin/" class="btn btn-secondary">Admin Panel</a></p>
                                      </div>';
                                
                            } catch (PDOException $e) {
                                echo '<div class="alert alert-danger">
                                        <h4>Installation Failed</h4>
                                        <p>Error: ' . htmlspecialchars($e->getMessage()) . '</p>
                                      </div>';
                            }
                        } else {
                        ?>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="host" class="form-label">Database Host</label>
                                <input type="text" class="form-control" id="host" name="host" value="localhost" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="dbname" class="form-label">Database Name</label>
                                <input type="text" class="form-control" id="dbname" name="dbname" value="kuderik_photo" required>
                                <div class="form-text">Database will be created if it doesn't exist</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">Database Username</label>
                                <input type="text" class="form-control" id="username" name="username" value="root" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Database Password</label>
                                <input type="password" class="form-control" id="password" name="password">
                            </div>
                            
                            <button type="submit" class="btn btn-primary">Install Database</button>
                        </form>
                        
                        <hr>
                        <div class="alert alert-info">
                            <h5>Requirements:</h5>
                            <ul class="mb-0">
                                <li>PHP 7.4 or higher</li>
                                <li>MySQL 5.7 or higher</li>
                                <li>GD extension for image processing</li>
                                <li>PDO MySQL extension</li>
                                <li>Write permissions on uploads/ directory</li>
                            </ul>
                        </div>
                        
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
