<?php
/**
 * Setup Enhanced Admin Database Schema
 * Run this script to create all the enhanced admin tables
 */

require_once 'config/database.php';

echo "🚀 STARTING ADMIN DATABASE SETUP!\n";
echo "=====================================\n\n";

try {
    // Get database instance
    $db = Database::getInstance();
    echo "✅ Database connection successful!\n\n";
    
    // Read and execute the admin schema
    $schema = file_get_contents('admin_database_schema.sql');
    
    if (!$schema) {
        throw new Exception("Could not read admin_database_schema.sql file");
    }
    
    echo "📋 Executing enhanced database schema...\n";
    
    // Clean up the schema - remove comments and split properly
    $lines = explode("\n", $schema);
    $cleanedLines = [];

    foreach ($lines as $line) {
        $line = trim($line);
        if (!empty($line) && !str_starts_with($line, '--')) {
            $cleanedLines[] = $line;
        }
    }

    $cleanedSchema = implode("\n", $cleanedLines);
    $statements = array_filter(array_map('trim', explode(';', $cleanedSchema)));

    $successCount = 0;
    $errorCount = 0;

    foreach ($statements as $statement) {
        if (empty($statement)) {
            continue;
        }

        try {
            $db->getConnection()->exec($statement . ';');
            $successCount++;

            // Extract table name for progress display
            if (preg_match('/CREATE TABLE.*?(\w+)/i', $statement, $matches)) {
                echo "  ✅ Created table: {$matches[1]}\n";
            } elseif (preg_match('/INSERT.*?INTO.*?(\w+)/i', $statement, $matches)) {
                echo "  ✅ Inserted data into: {$matches[1]}\n";
            } else {
                echo "  ✅ Executed statement\n";
            }

        } catch (Exception $e) {
            $errorCount++;
            echo "  ❌ Error: " . $e->getMessage() . "\n";
            echo "  📝 Statement: " . substr($statement, 0, 100) . "...\n";
        }
    }
    
    echo "\n📊 SETUP SUMMARY:\n";
    echo "================\n";
    echo "✅ Successful operations: $successCount\n";
    echo "❌ Errors: $errorCount\n\n";
    
    // Verify tables were created
    echo "🔍 VERIFYING TABLES:\n";
    echo "===================\n";
    
    $requiredTables = [
        'users',
        'client_profiles', 
        'galleries',
        'media_items',
        'gallery_permissions',
        'bookings',
        'orders',
        'order_items',
        'activity_log',
        'email_templates',
        'system_settings'
    ];
    
    $existingTables = [];
    
    // Check if we're using SQLite or MySQL
    try {
        // Try MySQL first
        $result = $db->fetchAll("SHOW TABLES");
        $existingTables = array_column($result, 'Tables_in_' . DB_NAME);
        $dbType = 'mysql';
    } catch (Exception $e) {
        try {
            // Fallback to SQLite
            $result = $db->fetchAll("SELECT name FROM sqlite_master WHERE type='table'");
            $existingTables = array_column($result, 'name');
            $dbType = 'sqlite';
        } catch (Exception $e2) {
            echo "❌ Could not verify tables: " . $e2->getMessage() . "\n";
            $existingTables = [];
            $dbType = 'unknown';
        }
    }
    
    $missingTables = [];
    foreach ($requiredTables as $table) {
        if (in_array($table, $existingTables)) {
            echo "  ✅ Table exists: $table\n";
        } else {
            echo "  ❌ Table missing: $table\n";
            $missingTables[] = $table;
        }
    }
    
    echo "\n";
    
    if (empty($missingTables)) {
        echo "🎉 ALL TABLES CREATED SUCCESSFULLY!\n";
        echo "=====================================\n\n";
        
        // Check for default admin user
        echo "👤 CHECKING DEFAULT ADMIN USER:\n";
        echo "==============================\n";
        
        try {
            $adminUser = $db->fetchOne("SELECT * FROM users WHERE role = 'admin' LIMIT 1");
            if ($adminUser) {
                echo "✅ Default admin user exists: " . $adminUser['email'] . "\n";
                echo "📧 Email: " . $adminUser['email'] . "\n";
                echo "👤 Name: " . $adminUser['first_name'] . " " . $adminUser['last_name'] . "\n";
                echo "🔑 Status: " . $adminUser['status'] . "\n";
            } else {
                echo "❌ No admin user found - creating default admin...\n";
                
                // Create default admin user
                $defaultPassword = 'admin123';
                $passwordHash = password_hash($defaultPassword, PASSWORD_DEFAULT);
                
                $db->query("
                    INSERT INTO users (username, email, password_hash, first_name, last_name, role, status, email_verified) 
                    VALUES (?, ?, ?, ?, ?, 'admin', 'active', 1)
                ", [
                    'admin',
                    '<EMAIL>',
                    $passwordHash,
                    'Admin',
                    'User'
                ]);
                
                echo "✅ Default admin user created!\n";
                echo "📧 Email: <EMAIL>\n";
                echo "🔑 Password: $defaultPassword\n";
                echo "⚠️  IMPORTANT: Change this password after first login!\n";
            }
        } catch (Exception $e) {
            echo "❌ Error checking admin user: " . $e->getMessage() . "\n";
        }
        
        echo "\n🎯 NEXT STEPS:\n";
        echo "=============\n";
        echo "1. ✅ Database setup complete!\n";
        echo "2. 🔄 Access admin panel: http://localhost:8000/admin/\n";
        echo "3. 🔄 Test client login: http://localhost:8000/client-login.php\n";
        echo "4. 🔄 Create upload directories\n";
        echo "5. 🔄 Test photo upload system\n";
        echo "6. 🔄 Configure PayPal settings\n\n";
        
        echo "🚀 PHASE 1 COMPLETE! Ready for Phase 2!\n";
        
    } else {
        echo "❌ SETUP INCOMPLETE!\n";
        echo "Missing tables: " . implode(', ', $missingTables) . "\n";
        echo "Please check the schema file and try again.\n";
    }
    
} catch (Exception $e) {
    echo "❌ SETUP FAILED!\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "Please check your database configuration and try again.\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Admin Database Setup Complete!\n";
echo str_repeat("=", 50) . "\n";
?>
