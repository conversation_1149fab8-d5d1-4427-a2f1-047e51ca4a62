<?php
require_once 'config/database.php';

try {
    $db = Database::getInstance();
    $result = $db->fetchAll('SHOW TABLES');
    
    echo "EXISTING TABLES:\n";
    echo "================\n";
    foreach ($result as $row) {
        echo "  " . array_values($row)[0] . "\n";
    }
    
    echo "\nTABLES NEEDED BY ADMIN:\n";
    echo "======================\n";
    $neededTables = ['galleries', 'media_items', 'contact_messages'];
    
    foreach ($neededTables as $table) {
        $exists = false;
        foreach ($result as $row) {
            if (array_values($row)[0] === $table) {
                $exists = true;
                break;
            }
        }
        echo "  $table: " . ($exists ? "✅ EXISTS" : "❌ MISSING") . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
