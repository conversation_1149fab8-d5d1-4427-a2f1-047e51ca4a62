<?php
/**
 * <PERSON><PERSON>ik Photo - Main Configuration File
 * Centralized configuration for routing, URLs, and site settings
 */

// Prevent direct access
if (!defined('KUDERIK_PHOTO')) {
    define('KUDERIK_PHOTO', true);
}

// Environment Configuration
define('ENVIRONMENT', 'development'); // development, staging, production

// Base URL Configuration
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
$port = $_SERVER['SERVER_PORT'] ?? '8000';

// Construct base URL
if (($protocol === 'http' && $port != '80') || ($protocol === 'https' && $port != '443')) {
    define('BASE_URL', $protocol . '://' . $host . ':' . $port);
} else {
    define('BASE_URL', $protocol . '://' . $host);
}

// Directory Configuration
define('ROOT_DIR', __DIR__);
define('INCLUDES_DIR', ROOT_DIR . '/includes');
define('PAGES_DIR', ROOT_DIR . '/pages');
define('ASSETS_DIR', ROOT_DIR . '/assets');
define('UPLOADS_DIR', ROOT_DIR . '/uploads');
define('CONFIG_DIR', ROOT_DIR . '/config');

// URL Configuration for SEO-friendly structure
define('SITE_URLS', [
    'home' => BASE_URL . '/',
    'about' => BASE_URL . '/about/',
    'services' => BASE_URL . '/services/',
    'portfolio' => BASE_URL . '/portfolio/',
    'contact' => BASE_URL . '/contact/',
    'gallery' => BASE_URL . '/gallery/',
    'admin' => BASE_URL . '/admin/',
]);

// Page Routing Configuration
define('PAGE_ROUTES', [
    // Main pages
    '' => 'pages/home.php',
    'home' => 'pages/home.php',
    'about' => 'about/index.php',
    'services' => 'services/index.php',
    'portfolio' => 'portfolio/index.php',
    'contact' => 'contact/index.php',
    
    // Gallery routes
    'gallery' => 'portfolio/index.php',
    'gallery/([a-zA-Z0-9-]+)' => 'gallery/index.php',
    
    // Admin routes
    'admin' => 'admin/index.php',
    'admin/login' => 'admin/login.php',
    'admin/logout' => 'admin/logout.php',
    'admin/galleries' => 'admin/galleries.php',
    'admin/media' => 'admin/media.php',
    'admin/settings' => 'admin/settings.php',
    'admin/messages' => 'admin/messages.php',
]);

// Navigation Menu Configuration
define('MAIN_NAVIGATION', [
    [
        'title' => 'Home',
        'url' => SITE_URLS['home'],
        'active_pages' => ['', 'home']
    ],
    [
        'title' => 'About',
        'url' => SITE_URLS['about'],
        'active_pages' => ['about']
    ],
    [
        'title' => 'Services',
        'url' => SITE_URLS['services'],
        'active_pages' => ['services']
    ],
    [
        'title' => 'Portfolio',
        'url' => SITE_URLS['portfolio'],
        'active_pages' => ['portfolio', 'gallery']
    ],
    [
        'title' => 'Contact',
        'url' => SITE_URLS['contact'],
        'active_pages' => ['contact']
    ]
]);

// Admin Navigation Configuration
define('ADMIN_NAVIGATION', [
    [
        'title' => 'Dashboard',
        'url' => SITE_URLS['admin'],
        'icon' => 'fas fa-tachometer-alt',
        'active_pages' => ['admin']
    ],
    [
        'title' => 'Galleries',
        'url' => SITE_URLS['admin'] . 'galleries/',
        'icon' => 'fas fa-images',
        'active_pages' => ['admin/galleries']
    ],
    [
        'title' => 'Media',
        'url' => SITE_URLS['admin'] . 'media/',
        'icon' => 'fas fa-photo-video',
        'active_pages' => ['admin/media']
    ],
    [
        'title' => 'Messages',
        'url' => SITE_URLS['admin'] . 'messages/',
        'icon' => 'fas fa-envelope',
        'active_pages' => ['admin/messages']
    ],
    [
        'title' => 'Settings',
        'url' => SITE_URLS['admin'] . 'settings/',
        'icon' => 'fas fa-cog',
        'active_pages' => ['admin/settings']
    ]
]);

// SEO Configuration
define('SEO_CONFIG', [
    'site_name' => 'Kuderik Photo',
    'default_title' => 'Kuderik Photo - Professional Photography & Video Production',
    'default_description' => 'Professional photography and video production services specializing in weddings, portraits, commercial, food, product, and real estate photography.',
    'default_keywords' => 'photography, video production, wedding photography, portrait photography, commercial photography, food photography, product photography, real estate photography, 360 tours',
    'og_image' => BASE_URL . '/assets/images/og-image.jpg',
    'twitter_handle' => '@kuderikphoto'
]);

// Database Configuration
define('DB_CONFIG', [
    'host' => 'localhost',
    'name' => 'kuderik_photo',
    'user' => 'kuderik_user',
    'pass' => 'kuderik_password_2024',
    'charset' => 'utf8mb4',
    'fallback' => 'sqlite'
]);

// Upload Configuration
define('UPLOAD_CONFIG', [
    'max_file_size' => 10 * 1024 * 1024, // 10MB
    'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'mp4', 'mov', 'avi'],
    'upload_path' => UPLOADS_DIR,
    'thumbnail_sizes' => [
        'small' => [300, 300],
        'medium' => [600, 600],
        'large' => [1200, 1200]
    ]
]);

// Helper Functions
function getUrl($page = '') {
    if (isset(SITE_URLS[$page])) {
        return SITE_URLS[$page];
    }
    return BASE_URL . '/' . ltrim($page, '/');
}

function getCurrentPage() {
    $request_uri = $_SERVER['REQUEST_URI'] ?? '/';
    $path = parse_url($request_uri, PHP_URL_PATH);
    return trim($path, '/');
}

function isActivePage($pages) {
    $current = getCurrentPage();
    if (is_array($pages)) {
        return in_array($current, $pages);
    }
    return $current === $pages;
}

function getSeoTitle($page_title = '') {
    if (empty($page_title)) {
        return SEO_CONFIG['default_title'];
    }
    return $page_title . ' - ' . SEO_CONFIG['site_name'];
}

function getSeoDescription($page_description = '') {
    return !empty($page_description) ? $page_description : SEO_CONFIG['default_description'];
}

// Load existing configuration files
if (file_exists(CONFIG_DIR . '/constants.php')) {
    require_once CONFIG_DIR . '/constants.php';
}

if (file_exists(CONFIG_DIR . '/database.php')) {
    require_once CONFIG_DIR . '/database.php';
}

// Initialize session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
